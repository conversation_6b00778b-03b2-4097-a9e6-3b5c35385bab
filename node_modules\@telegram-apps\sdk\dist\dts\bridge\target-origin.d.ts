/**
 * Sets a new global targetOrigin, used by the `postEvent` method.
 * The default value is "https://web.telegram.org".
 * You don't need to use this method until you know what you are doing.
 *
 * This method could be used for test purposes.
 * @param value - new target origin.
 * @see postEvent
 */
export declare function setTargetOrigin(value: string): void;
/**
 * Sets the initial target origin.
 */
export declare function resetTargetOrigin(): void;
/**
 * Returns current global target origin.
 */
export declare function targetOrigin(): string;
