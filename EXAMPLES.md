# Примеры использования NFT Gifts Mini App

## 🎮 Пользовательские сценарии

### Сценарий 1: Новый пользователь
1. Пользователь нажимает `/start` в боте
2. Получает приветственное сообщение с кнопкой "🎮 Open App"
3. Открывает Mini App
4. Видит демо-сундук и может открыть его бесплатно
5. Получает первый NFT подарок
6. Изучает интерфейс и другие функции

### Сценарий 2: Покупка сундука
1. Пользователь выбирает Bronze Chest (50 ⭐)
2. Нажимает на сундук
3. Система проверяет баланс Stars
4. Запускается анимация открытия
5. Пользователь получает случайный NFT
6. NFT добавляется в галерею

### Сценарий 3: Участие в лотерее
1. Пользователь переходит на вкладку "Lottery"
2. Видит текущий розыгрыш и призы
3. Покупает билет за 25 ⭐
4. Получает уникальный номер билета
5. Ждет окончания розыгрыша
6. Получает уведомление о результатах

## 🔧 Технические примеры

### Интеграция с TON Connect

```typescript
// Подключение кошелька
import { useTonConnectUI } from '@tonconnect/ui-react'

const [tonConnectUI] = useTonConnectUI()

const connectWallet = async () => {
  await tonConnectUI.connectWallet()
}

// Отправка транзакции
const sendTransaction = async () => {
  const transaction = {
    validUntil: Math.floor(Date.now() / 1000) + 60,
    messages: [
      {
        address: "0:...", // адрес получателя
        amount: "100000000", // 0.1 TON в nanotons
      }
    ]
  }
  
  await tonConnectUI.sendTransaction(transaction)
}
```

### Работа с Lottie анимациями

```typescript
// Компонент для отображения анимированного NFT
import lottie from 'lottie-web'

const AnimatedNFT = ({ lottieUrl }: { lottieUrl: string }) => {
  const containerRef = useRef<HTMLDivElement>(null)
  
  useEffect(() => {
    if (!containerRef.current) return
    
    const animation = lottie.loadAnimation({
      container: containerRef.current,
      renderer: 'svg',
      loop: true,
      autoplay: true,
      path: lottieUrl
    })
    
    return () => animation.destroy()
  }, [lottieUrl])
  
  return <div ref={containerRef} className="w-full h-full" />
}
```

### Telegram Web App API

```typescript
// Отправка данных в бот
const sendDataToBot = (data: any) => {
  if (window.Telegram?.WebApp) {
    window.Telegram.WebApp.sendData(JSON.stringify(data))
  }
}

// Использование тактильной обратной связи
const hapticFeedback = {
  success: () => window.Telegram?.WebApp.HapticFeedback.notificationOccurred('success'),
  error: () => window.Telegram?.WebApp.HapticFeedback.notificationOccurred('error'),
  impact: () => window.Telegram?.WebApp.HapticFeedback.impactOccurred('medium')
}
```

## 🎨 Кастомизация дизайна

### Изменение цветовой схемы

```css
/* src/index.css */
:root {
  --tg-color-bg: #ffffff;
  --tg-color-text: #000000;
  --custom-primary: #3B82F6;
  --custom-secondary: #8B5CF6;
}

[data-theme="dark"] {
  --tg-color-bg: #212121;
  --tg-color-text: #ffffff;
  --custom-primary: #60A5FA;
  --custom-secondary: #A78BFA;
}
```

### Создание нового типа сундука

```typescript
// Добавление в TreasureChest.tsx
const customChest: ChestType = {
  id: 'platinum',
  name: 'Platinum Chest',
  description: 'Ultra premium chest with guaranteed epic+',
  price: { ton: 0.5 },
  image: '💎',
  rewards: [
    { rarity: 'epic', chance: 70, items: ['platinum-epic-1', 'platinum-epic-2'] },
    { rarity: 'legendary', chance: 30, items: ['platinum-legendary-1'] }
  ]
}
```

## 📊 Аналитика и метрики

### Отслеживание событий

```typescript
// Трекинг открытия сундуков
const trackChestOpened = (chestType: string, reward: NFTGift) => {
  // Отправка в аналитику
  analytics.track('chest_opened', {
    chest_type: chestType,
    reward_rarity: reward.rarity,
    reward_value: reward.value,
    user_id: user?.id
  })
}

// Трекинг покупки билетов
const trackTicketPurchased = (drawId: string, paymentMethod: string) => {
  analytics.track('lottery_ticket_purchased', {
    draw_id: drawId,
    payment_method: paymentMethod,
    user_id: user?.id
  })
}
```

## 🔗 Интеграция с маркетплейсами

### Fragment API

```typescript
// Получение данных NFT с Fragment
const fetchNFTFromFragment = async (nftId: string) => {
  const response = await fetch(`https://nft.fragment.com/gift/${nftId}.json`)
  const metadata = await response.json()
  
  return {
    id: nftId,
    name: metadata.name,
    description: metadata.description,
    image: metadata.image,
    lottieUrl: metadata.animation_url,
    attributes: metadata.attributes
  }
}
```

### GetGems интеграция

```typescript
// Поиск NFT на GetGems
const searchOnGetGems = async (query: string) => {
  const response = await fetch(`https://api.getgems.io/search?q=${query}`)
  return response.json()
}
```

## 🤖 Расширение функциональности бота

### Добавление новых команд

```javascript
// bot/index.js
bot.onText(/\/leaderboard/, (msg) => {
  const chatId = msg.chat.id
  
  // Получение топ пользователей из базы данных
  const topUsers = getTopCollectors()
  
  let message = '🏆 <b>Top Collectors</b>\n\n'
  topUsers.forEach((user, index) => {
    const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`
    message += `${medal} ${user.name} - ${user.nftCount} NFTs\n`
  })
  
  bot.sendMessage(chatId, message, { parse_mode: 'HTML' })
})
```

### Уведомления о событиях

```javascript
// Уведомление о выигрыше в лотерее
const notifyLotteryWinner = (userId, prize) => {
  bot.sendMessage(userId, `🎉 Поздравляем! Вы выиграли в лотерее: ${prize.name}!`, {
    reply_markup: {
      inline_keyboard: [
        [{ text: '🎮 Открыть приложение', web_app: { url: webAppUrl } }]
      ]
    }
  })
}
```

## 🔒 Безопасность

### Валидация данных

```typescript
// Валидация данных от Web App
const validateWebAppData = (data: string, hash: string) => {
  const secret = crypto.createHmac('sha256', 'WebAppData').update(botToken).digest()
  const calculatedHash = crypto.createHmac('sha256', secret).update(data).digest('hex')
  return calculatedHash === hash
}
```

### Защита от спама

```javascript
// Ограничение частоты запросов
const rateLimiter = new Map()

const checkRateLimit = (userId) => {
  const now = Date.now()
  const userLimit = rateLimiter.get(userId) || { count: 0, resetTime: now + 60000 }
  
  if (now > userLimit.resetTime) {
    userLimit.count = 0
    userLimit.resetTime = now + 60000
  }
  
  if (userLimit.count >= 10) {
    return false // Превышен лимит
  }
  
  userLimit.count++
  rateLimiter.set(userId, userLimit)
  return true
}
```

## 📱 Тестирование

### Unit тесты для компонентов

```typescript
// __tests__/TreasureChest.test.tsx
import { render, fireEvent, screen } from '@testing-library/react'
import TreasureChest from '../src/components/TreasureChest'

test('opens demo chest successfully', async () => {
  const mockBalance = { stars: 100, ton: 0.5, tickets: 3 }
  const mockUpdateBalance = jest.fn()
  
  render(
    <TreasureChest 
      balance={mockBalance} 
      onBalanceUpdate={mockUpdateBalance} 
    />
  )
  
  const demoChest = screen.getByText('Demo Chest')
  fireEvent.click(demoChest)
  
  // Проверяем, что анимация запустилась
  expect(screen.getByText('Opening Demo Chest...')).toBeInTheDocument()
})
```

### E2E тесты

```typescript
// e2e/app.spec.ts
import { test, expect } from '@playwright/test'

test('user can open chest and receive NFT', async ({ page }) => {
  await page.goto('http://localhost:5173')
  
  // Ждем загрузки приложения
  await page.waitForSelector('[data-testid="demo-chest"]')
  
  // Кликаем на демо-сундук
  await page.click('[data-testid="demo-chest"]')
  
  // Ждем появления модального окна с наградой
  await page.waitForSelector('[data-testid="reward-modal"]')
  
  // Проверяем, что NFT был получен
  const rewardTitle = await page.textContent('[data-testid="reward-title"]')
  expect(rewardTitle).toContain('Gift')
})
```

---

Эти примеры помогут вам расширить и кастомизировать ваше NFT Gifts Mini App! 🚀
