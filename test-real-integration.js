// Тестирование реальной интеграции с TON API для Fragment NFT
console.log('🔍 Testing real TON API integration...')

const TON_API_KEY = '7125266008:c0ad27aa61ea792a52b4e7b39f9b1ed46751'
const TON_API_BASE = 'https://tonapi.io/v2'

// Известный адрес Fragment коллекции Hex Pots
const HEX_POTS_COLLECTION = '0:7a02d04f3ae4ed42697ca1203b5fc66a27942670e618aa8cfddac4185c6510af'

async function testRealIntegration() {
  try {
    console.log('🚀 Starting real TON API integration test...')
    
    // Тест 1: Получить информацию о коллекции Hex Pots
    console.log('\n📦 Test 1: Get Hex Pots collection info...')
    const collectionInfo = await getCollectionInfo(HEX_POTS_COLLECTION)
    
    if (collectionInfo) {
      console.log('✅ Collection Info:')
      console.log(`  Name: ${collectionInfo.name}`)
      console.log(`  Description: ${collectionInfo.description}`)
      console.log(`  Total Items: ${collectionInfo.next_item_index}`)
    }
    
    // Тест 2: Получить NFT из коллекции
    console.log('\n🎁 Test 2: Get NFTs from Hex Pots collection...')
    const nfts = await getCollectionNFTs(HEX_POTS_COLLECTION, 10)
    
    if (nfts.length > 0) {
      console.log(`✅ Found ${nfts.length} NFTs:`)
      nfts.forEach((nft, index) => {
        const price = nft.sale?.price ? (parseFloat(nft.sale.price) / 1000000000).toFixed(3) : 'Not for sale'
        console.log(`  ${index + 1}. ${nft.metadata?.name} - ${price} TON`)
      })
      
      // Тест 3: Получить детальную информацию о первом NFT
      if (nfts[0]) {
        console.log('\n🔍 Test 3: Get detailed NFT info...')
        const nftDetails = await getNFTDetails(nfts[0].address)
        
        if (nftDetails) {
          console.log('✅ NFT Details:')
          console.log(`  Address: ${nftDetails.address}`)
          console.log(`  Name: ${nftDetails.metadata?.name}`)
          console.log(`  Image: ${nftDetails.metadata?.image}`)
          console.log(`  Lottie: ${nftDetails.metadata?.lottie}`)
          console.log(`  Owner: ${nftDetails.owner?.address}`)
          
          if (nftDetails.sale) {
            const price = parseFloat(nftDetails.sale.price.value) / 1000000000
            console.log(`  Sale Price: ${price.toFixed(3)} TON`)
            console.log(`  Marketplace: ${nftDetails.sale.marketplace}`)
          }
        }
      }
    }
    
    // Тест 4: Поиск других Fragment коллекций
    console.log('\n🔍 Test 4: Search for Fragment collections...')
    const fragmentCollections = await searchFragmentCollections()
    
    if (fragmentCollections.length > 0) {
      console.log(`✅ Found ${fragmentCollections.length} Fragment collections:`)
      fragmentCollections.forEach((collection, index) => {
        console.log(`  ${index + 1}. ${collection.metadata?.name} (${collection.address})`)
      })
    }
    
    // Тест 5: Получить NFT на продаже
    console.log('\n💰 Test 5: Get NFTs for sale...')
    const salesData = await getNFTsForSale()
    
    if (salesData.length > 0) {
      console.log(`✅ Found ${salesData.length} NFTs for sale:`)
      salesData.slice(0, 5).forEach((sale, index) => {
        const price = parseFloat(sale.price.value) / 1000000000
        console.log(`  ${index + 1}. ${sale.nft?.metadata?.name || 'Unknown'} - ${price.toFixed(3)} TON`)
      })
    }
    
    return {
      collectionInfo,
      nfts,
      fragmentCollections,
      salesData
    }
    
  } catch (error) {
    console.error('💥 Integration test failed:', error)
    return null
  }
}

async function getCollectionInfo(address) {
  try {
    const response = await fetch(`${TON_API_BASE}/nfts/collections/${address}`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    console.error(`Failed to get collection info: ${error.message}`)
    return null
  }
}

async function getCollectionNFTs(collectionAddress, limit = 20) {
  try {
    const response = await fetch(`${TON_API_BASE}/nfts/collections/${collectionAddress}/items?limit=${limit}`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    return data.nft_items || []
  } catch (error) {
    console.error(`Failed to get collection NFTs: ${error.message}`)
    return []
  }
}

async function getNFTDetails(address) {
  try {
    const response = await fetch(`${TON_API_BASE}/nfts/${address}`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    console.error(`Failed to get NFT details: ${error.message}`)
    return null
  }
}

async function searchFragmentCollections() {
  try {
    const response = await fetch(`${TON_API_BASE}/nfts/collections?limit=50`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    
    // Фильтруем коллекции, которые могут быть Fragment
    const fragmentCollections = data.nft_collections?.filter(collection => {
      const name = collection.metadata?.name?.toLowerCase() || ''
      const description = collection.metadata?.description?.toLowerCase() || ''
      
      return name.includes('gift') ||
             name.includes('fragment') ||
             name.includes('telegram') ||
             description.includes('telegram') ||
             description.includes('fragment') ||
             description.includes('exclusive nft collection')
    }) || []

    return fragmentCollections
  } catch (error) {
    console.error(`Failed to search collections: ${error.message}`)
    return []
  }
}

async function getNFTsForSale() {
  try {
    const response = await fetch(`${TON_API_BASE}/nfts/sales?limit=20`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    return data.sales?.filter(sale => sale.price?.token_name === 'TON') || []
  } catch (error) {
    console.error(`Failed to get sales: ${error.message}`)
    return []
  }
}

// Запускаем тест
testRealIntegration().then(results => {
  if (results) {
    console.log('\n🎉 Real TON API integration test completed successfully!')
    console.log('\n📊 Summary:')
    console.log(`✅ Collection Info: ${results.collectionInfo ? 'SUCCESS' : 'FAILED'}`)
    console.log(`✅ NFTs Found: ${results.nfts?.length || 0}`)
    console.log(`✅ Fragment Collections: ${results.fragmentCollections?.length || 0}`)
    console.log(`✅ Sales Data: ${results.salesData?.length || 0}`)
    
    if (results.nfts && results.nfts.length > 0) {
      const pricesForSale = results.nfts
        .filter(nft => nft.sale?.price)
        .map(nft => parseFloat(nft.sale.price) / 1000000000)
      
      if (pricesForSale.length > 0) {
        console.log(`\n💰 Price Statistics:`)
        console.log(`  Highest: ${Math.max(...pricesForSale).toFixed(3)} TON`)
        console.log(`  Lowest: ${Math.min(...pricesForSale).toFixed(3)} TON`)
        console.log(`  Average: ${(pricesForSale.reduce((a, b) => a + b, 0) / pricesForSale.length).toFixed(3)} TON`)
      }
    }
    
    console.log('\n🚀 Ready to use real Fragment NFT data in the app!')
  } else {
    console.log('\n😞 Integration test failed')
  }
}).catch(error => {
  console.error('💥 Test error:', error)
})
