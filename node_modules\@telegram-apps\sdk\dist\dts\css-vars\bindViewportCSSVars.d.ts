import { Viewport } from '../components/Viewport/Viewport.js';
import { CleanupFn } from '../types/index.js';

export interface GetViewportCSSVarNameFn {
    /**
     * @param property - Viewport property.
     * @returns Computed complete CSS variable name.
     */
    (property: 'width' | 'height' | 'stable-height'): string;
}
/**
 * Accepts Viewport instance and sets CSS variables connected with viewport
 * sizes.
 *
 * Be careful using this function as long as it can impact application
 * performance. Viewport size is changing rather often, this makes CSS
 * variables update, which leads to possible layout redraw.
 *
 * Variables:
 * - `--tg-viewport-height`
 * - `--tg-viewport-width`
 * - `--tg-viewport-stable-height`
 *
 * Variables are being automatically updated in case, corresponding properties
 * updated in passed Viewport instance.
 *
 * @param viewport - Viewport instance.
 * @param getCSSVarName - function, returning complete CSS variable name for the specified
 * Viewport property.
 * @returns Function to stop updating variables.
 */
export declare function bindViewportCSSVars(viewport: Viewport, getCSSVarName?: GetViewportCSSVarNameFn): CleanupFn;
