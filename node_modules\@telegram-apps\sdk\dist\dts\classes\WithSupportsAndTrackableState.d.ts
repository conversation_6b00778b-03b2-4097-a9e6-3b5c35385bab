import { WithSupportsAndStateUtils } from './WithSupportsAndStateUtils.js';
import { StateEvents } from './State/types.js';
import { EventEmitter } from '../events/event-emitter/EventEmitter.js';

type Emitter<StateShape extends object> = EventEmitter<StateEvents<StateShape>>;
export declare class WithSupportsAndTrackableState<StateShape extends object, SupportsMethod extends string> extends WithSupportsAndStateUtils<StateShape, SupportsMethod> {
    /**
     * Adds a new event listener.
     */
    on: Emitter<StateShape>['on'];
    /**
     * Removes the event listener.
     */
    off: Emitter<StateShape>['off'];
}
export {};
