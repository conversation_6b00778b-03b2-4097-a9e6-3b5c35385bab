import { WithSupports } from '../../classes/WithSupports.js';
import { PostEvent } from '../../bridge/methods/postEvent.js';
import { CreateRequestIdFn } from '../../request-id/types.js';
import { ExecuteWithTimeout } from '../../types/methods.js';
import { Version } from '../../version/types.js';

/**
 * @see API: https://docs.telegram-mini-apps.com/packages/telegram-apps-sdk/components/cloud-storage
 */
export declare class CloudStorage extends WithSupports<'delete' | 'get' | 'getKeys' | 'set'> {
    private readonly createRequestId;
    private readonly postEvent;
    constructor(version: Version, createRequestId: CreateRequestIdFn, postEvent: PostEvent);
    /**
     * Deletes specified key or keys from the cloud storage.
     * @param keyOrKeys - key or keys to delete.
     * @param options - request execution options.
     */
    delete(keyOrKeys: string | string[], options?: ExecuteWithTimeout): Promise<void>;
    /**
     * Returns list of all keys presented in the cloud storage.
     * @param options - request execution options.
     */
    getKeys(options?: ExecuteWithTimeout): Promise<string[]>;
    /**
     * Returns map, where key is one of the specified in keys argument, and value is according
     * storage value.
     * @param keys - keys list.
     * @param options - request execution options.
     */
    get<K extends string>(keys: K[], options?: ExecuteWithTimeout): Promise<Record<K, string>>;
    /**
     * Returns value of the specified key.
     * @param key - cloud storage key.
     * @param options - request execution options.
     * @return Value of the specified key. In case, key was not created previously, function
     * will return empty string.
     */
    get(key: string, options?: ExecuteWithTimeout): Promise<string>;
    /**
     * Saves specified value by key.
     * @param key - storage key.
     * @param value - storage value.
     * @param options - request execution options.
     */
    set(key: string, value: string, options?: ExecuteWithTimeout): Promise<void>;
}
