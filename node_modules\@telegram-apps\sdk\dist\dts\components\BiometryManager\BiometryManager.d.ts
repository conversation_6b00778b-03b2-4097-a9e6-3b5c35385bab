import { WithSupportsAndTrackableState } from '../../classes/WithSupportsAndTrackableState.js';
import { BiometryType } from '../../bridge/events/types.js';
import { BiometryManagerAuthenticateOptions, BiometryManagerProps, BiometryManagerRequestAccessOptions, BiometryManagerState, BiometryManagerUpdateTokenOptions } from './types.js';

export declare class BiometryManager extends WithSupportsAndTrackableState<BiometryManagerState, 'auth' | 'openSettings' | 'requestAccess' | 'updateToken'> {
    private readonly postEvent;
    private authPromise?;
    private accessPromise?;
    constructor({ postEvent, version, ...rest }: BiometryManagerProps);
    /**
     * Shows whether biometry is available.
     */
    get available(): boolean;
    /**
     * Shows whether permission to use biometrics has been granted.
     */
    get accessGranted(): boolean;
    /**
     * Shows whether if permission to use biometrics has been requested.
     */
    get accessRequested(): boolean;
    /**
     * Authenticates the user using biometrics.
     * @param options - method options.
     * @since 7.2
     * @returns Token from the local secure storage, if authentication was successful.
     */
    authenticate({ reason, ...rest }: BiometryManagerAuthenticateOptions): Promise<string | undefined>;
    /**
     * A unique device identifier that can be used to match the token to the device.
     */
    get deviceId(): string;
    /**
     * Opens the biometric access settings for bots. Useful when you need to request biometrics
     * access to users who haven't granted it yet.
     *
     * _Note that this method can be called only in response to user interaction with the Mini App
     * interface (e.g. a click inside the Mini App or on the main button)_.
     * @since 7.2
     */
    openSettings(): void;
    /**
     * Requests permission to use biometrics.
     * @since 7.2
     * @returns Promise with true, if access was granted.
     */
    requestAccess({ reason, ...rest }?: BiometryManagerRequestAccessOptions): Promise<boolean>;
    /**
     * The type of biometrics currently available on the device.
     */
    get biometryType(): BiometryType | undefined;
    /**
     * Shows whether token was saved previously in the local secure storage.
     */
    get tokenSaved(): boolean;
    /**
     * Updates the biometric token in a secure storage on the device.
     * @returns Promise with `true`, if token was updated.
     */
    updateToken({ token, ...rest }?: BiometryManagerUpdateTokenOptions): Promise<boolean>;
}
