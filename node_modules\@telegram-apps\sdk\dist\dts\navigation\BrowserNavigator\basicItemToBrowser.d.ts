import { BasicNavigatorHistoryItem } from '../BasicNavigator/types.js';
import { BrowserNavigatorHistoryItem, BrowserNavigatorHistoryItemParams } from './types.js';

/**
 * Converts basic navigator entry to browser navigator entry.
 */
export declare function basicItemToBrowser<State>({ params, ...rest }: BasicNavigatorHistoryItem<BrowserNavigatorHistoryItemParams<State>>): BrowserNavigatorHistoryItem<State>;
