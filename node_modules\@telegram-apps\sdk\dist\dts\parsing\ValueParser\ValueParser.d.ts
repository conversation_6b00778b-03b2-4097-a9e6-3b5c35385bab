import { Parser } from '../types.js';
import { ValueParserOptionalResult, ValueParserParseResult } from './types.js';

export declare class ValueParser<ResultType, IsOptional extends boolean> {
    protected parser: Parser<ResultType>;
    protected isOptional: IsOptional;
    protected type?: string | undefined;
    constructor(parser: Parser<ResultType>, isOptional: IsOptional, type?: string | undefined);
    /**
     * Attempts to parse passed value
     * @param value - value to parse.
     * @throws {SDKError} ERR_PARSE
     * @see ERR_PARSE
     */
    parse(value: unknown): ValueParserParseResult<ResultType, IsOptional>;
    optional(): ValueParserOptionalResult<this, ResultType>;
}
