// Отладка TON API
console.log('🔍 Debugging TON API...')

const TON_API_KEY = '7125266008:c0ad27aa61ea792a52b4e7b39f9b1ed46751'
const TON_API_BASE = 'https://tonapi.io/v2'

// Известный адрес Hex Pot NFT
const HEX_POT_NFT = '0:3a4f57aded85be8f7d1651b8e9d7749662014e08f6bcd3afaf2175c83188a7da'
const HEX_POTS_COLLECTION = '0:7a02d04f3ae4ed42697ca1203b5fc66a27942670e618aa8cfddac4185c6510af'

async function debugAPI() {
  try {
    console.log('🚀 Starting API debug...')
    
    // Тест 1: Проверяем конкретный NFT
    console.log('\n📦 Test 1: Check specific Hex Pot NFT...')
    const nftResponse = await fetch(`${TON_API_BASE}/nfts/${HEX_POT_NFT}`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })
    
    console.log(`Status: ${nftResponse.status} ${nftResponse.statusText}`)
    
    if (nftResponse.ok) {
      const nftData = await nftResponse.json()
      console.log('✅ NFT Data:')
      console.log(`  Name: ${nftData.metadata?.name}`)
      console.log(`  Image: ${nftData.metadata?.image}`)
      console.log(`  Collection: ${nftData.collection?.address}`)
      console.log(`  Collection Name: ${nftData.collection?.name}`)
    } else {
      console.log('❌ NFT request failed')
      const errorText = await nftResponse.text()
      console.log('Error:', errorText)
    }
    
    // Тест 2: Проверяем коллекцию
    console.log('\n📚 Test 2: Check Hex Pots collection...')
    const collectionResponse = await fetch(`${TON_API_BASE}/nfts/collections/${HEX_POTS_COLLECTION}`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })
    
    console.log(`Status: ${collectionResponse.status} ${collectionResponse.statusText}`)
    
    if (collectionResponse.ok) {
      const collectionData = await collectionResponse.json()
      console.log('✅ Collection Data:')
      console.log(`  Name: ${collectionData.metadata?.name}`)
      console.log(`  Description: ${collectionData.metadata?.description}`)
      console.log(`  Next Item Index: ${collectionData.next_item_index}`)
    } else {
      console.log('❌ Collection request failed')
      const errorText = await collectionResponse.text()
      console.log('Error:', errorText)
    }
    
    // Тест 3: Получаем NFT из коллекции
    console.log('\n🎁 Test 3: Get NFTs from collection...')
    const itemsResponse = await fetch(`${TON_API_BASE}/nfts/collections/${HEX_POTS_COLLECTION}/items?limit=5`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })
    
    console.log(`Status: ${itemsResponse.status} ${itemsResponse.statusText}`)
    
    if (itemsResponse.ok) {
      const itemsData = await itemsResponse.json()
      console.log('✅ Items Data:')
      console.log(`  Total items: ${itemsData.nft_items?.length || 0}`)
      
      if (itemsData.nft_items && itemsData.nft_items.length > 0) {
        itemsData.nft_items.forEach((item, index) => {
          console.log(`  ${index + 1}. ${item.metadata?.name} (${item.address})`)
        })
      }
    } else {
      console.log('❌ Items request failed')
      const errorText = await itemsResponse.text()
      console.log('Error:', errorText)
    }
    
    // Тест 4: Проверяем другие эндпоинты
    console.log('\n🔍 Test 4: Try alternative endpoints...')
    
    const alternativeEndpoints = [
      `${TON_API_BASE}/blockchain/accounts/${HEX_POT_NFT}`,
      `${TON_API_BASE}/accounts/${HEX_POT_NFT}/nfts`,
      `${TON_API_BASE}/nfts/search?query=hex pot`,
    ]
    
    for (const endpoint of alternativeEndpoints) {
      try {
        console.log(`  🔍 Trying: ${endpoint}`)
        const response = await fetch(endpoint, {
          headers: {
            'X-API-Key': TON_API_KEY,
            'Accept': 'application/json'
          }
        })
        
        console.log(`    Status: ${response.status}`)
        
        if (response.ok) {
          const data = await response.json()
          console.log(`    ✅ Success! Data keys:`, Object.keys(data))
        }
      } catch (e) {
        console.log(`    ❌ Error: ${e.message}`)
      }
    }
    
    // Тест 5: Проверяем лимиты API
    console.log('\n⚡ Test 5: Check API limits...')
    const testResponse = await fetch(`${TON_API_BASE}/nfts/collections?limit=1`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })
    
    console.log(`Status: ${testResponse.status}`)
    console.log('Headers:')
    for (const [key, value] of testResponse.headers.entries()) {
      if (key.includes('rate') || key.includes('limit') || key.includes('remaining')) {
        console.log(`  ${key}: ${value}`)
      }
    }
    
    if (testResponse.ok) {
      const data = await testResponse.json()
      console.log(`✅ API working, got ${data.nft_collections?.length || 0} collections`)
    }
    
  } catch (error) {
    console.error('💥 Debug failed:', error)
  }
}

// Запускаем отладку
debugAPI().then(() => {
  console.log('\n🏁 Debug completed!')
}).catch(error => {
  console.error('💥 Debug error:', error)
})
