import { useState, useEffect } from 'react'
import { TonConnectButton } from '@tonconnect/ui-react'
import { Gift, Sparkles, Trophy, Ticket } from 'lucide-react'
import TreasureChest from './components/TreasureChest'
import LotterySection from './components/LotterySection'
import NFTGallery from './components/NFTGallery'
import UserStats from './components/UserStats'
import { useTelegramWebApp } from './hooks/useTelegramWebApp'

function App() {
  const [activeTab, setActiveTab] = useState<'chests' | 'lottery' | 'gallery'>('chests')
  const [userBalance, setUserBalance] = useState({ stars: 100, ton: 0.5, tickets: 3 })
  const { webApp, user } = useTelegramWebApp()

  useEffect(() => {
    if (webApp) {
      // Configure main button
      webApp.MainButton.setText('Open Chest')
      webApp.MainButton.show()

      // Handle back button
      webApp.BackButton.onClick(() => {
        if (activeTab !== 'chests') {
          setActiveTab('chests')
        } else {
          webApp.close()
        }
      })
    }
  }, [webApp, activeTab])

  const tabs = [
    { id: 'chests' as const, label: 'Chests', icon: Gift },
    { id: 'lottery' as const, label: 'Lottery', icon: Trophy },
    { id: 'gallery' as const, label: 'Gallery', icon: Sparkles },
  ]

  return (
    <div className="min-h-screen text-tg-text safe-area-top safe-area-bottom">
      {/* Header */}
      <header className="sticky top-0 z-50 glass-effect border-b border-white/10">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative w-10 h-10 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
                <Gift className="w-6 h-6 text-white" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  NFT Gifts
                </h1>
                {user && (
                  <p className="text-sm text-tg-hint">
                    Welcome, {user.first_name}! 👋
                  </p>
                )}
              </div>
            </div>
            <div className="scale-90">
              <TonConnectButton />
            </div>
          </div>
        </div>
      </header>

      {/* User Stats */}
      <UserStats balance={userBalance} />

      {/* Tab Navigation */}
      <nav className="px-4 py-2">
        <div className="flex backdrop-blur-sm rounded-2xl p-1 border border-white/10" style={{backgroundColor: 'rgba(var(--tg-secondary-bg-rgb, 24, 24, 24), 0.5)'}}>
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-3 rounded-xl transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg scale-105'
                    : 'text-tg-hint hover:text-tg-text'
                }`}
                style={activeTab !== tab.id ? {':hover': {backgroundColor: 'rgba(255, 255, 255, 0.05)'}} : {}}
              >
                <Icon className={`w-4 h-4 ${activeTab === tab.id ? 'animate-pulse' : ''}`} />
                <span className="text-sm font-medium">{tab.label}</span>
              </button>
            )
          })}
        </div>
      </nav>

      {/* Main Content */}
      <main className="px-4 pb-20">
        {activeTab === 'chests' && (
          <TreasureChest
            balance={userBalance}
            onBalanceUpdate={setUserBalance}
          />
        )}
        {activeTab === 'lottery' && (
          <LotterySection
            balance={userBalance}
            onBalanceUpdate={setUserBalance}
          />
        )}
        {activeTab === 'gallery' && <NFTGallery />}
      </main>
    </div>
  )
}

export default App
