// Тестовый скрипт для парсинга HTML страниц Fragment
console.log('🔍 Testing Fragment HTML parsing...')

async function parseFragmentGiftsPage() {
  try {
    console.log('🔍 Fetching Fragment gifts page...')
    
    const response = await fetch('https://fragment.com/gifts', {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Cache-Control': 'no-cache'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const html = await response.text()
    console.log(`✅ Got HTML page (${html.length} characters)`)

    // Ищем JSON данные в HTML
    console.log('🔍 Looking for embedded JSON data...')
    
    const patterns = [
      /window\.__INITIAL_STATE__\s*=\s*({.*?});/s,
      /window\.__DATA__\s*=\s*({.*?});/s,
      /window\.initData\s*=\s*({.*?});/s,
      /"gifts":\s*(\[.*?\])/s,
      /"items":\s*(\[.*?\])/s,
      /data-gifts\s*=\s*"([^"]*?)"/g,
      /data-items\s*=\s*"([^"]*?)"/g
    ]

    for (let i = 0; i < patterns.length; i++) {
      const pattern = patterns[i]
      const matches = html.match(pattern)
      
      if (matches) {
        console.log(`🎯 Found JSON with pattern ${i + 1}:`, matches[1].substring(0, 200) + '...')
        
        try {
          const jsonData = JSON.parse(matches[1])
          console.log('✅ Successfully parsed JSON:', jsonData)
          
          if (jsonData.gifts || jsonData.items || Array.isArray(jsonData)) {
            console.log('🎁 Found gifts data!')
            return jsonData
          }
        } catch (e) {
          console.log(`❌ Failed to parse JSON from pattern ${i + 1}:`, e.message)
        }
      }
    }

    // Ищем HTML элементы с подарками
    console.log('🔍 Looking for gift HTML elements...')
    
    const giftPatterns = [
      /<div[^>]*class="[^"]*gift[^"]*"[^>]*>.*?<\/div>/gs,
      /<div[^>]*data-gift[^>]*>.*?<\/div>/gs,
      /<article[^>]*class="[^"]*item[^"]*"[^>]*>.*?<\/article>/gs,
      /<div[^>]*class="[^"]*item[^"]*"[^>]*>.*?<\/div>/gs
    ]

    for (let i = 0; i < giftPatterns.length; i++) {
      const pattern = giftPatterns[i]
      const matches = html.match(pattern)
      
      if (matches && matches.length > 0) {
        console.log(`🎯 Found ${matches.length} gift elements with pattern ${i + 1}`)
        console.log('Sample element:', matches[0].substring(0, 300) + '...')
        
        // Пытаемся извлечь данные из HTML элементов
        const gifts = parseGiftElements(matches.slice(0, 5)) // Берем первые 5 для теста
        if (gifts.length > 0) {
          console.log('✅ Successfully parsed gift elements:', gifts)
          return gifts
        }
      }
    }

    // Ищем ссылки на подарки
    console.log('🔍 Looking for gift links...')
    const linkPattern = /href="\/gift\/([^"]+)"/g
    const links = []
    let match
    
    while ((match = linkPattern.exec(html)) !== null) {
      links.push(match[1])
    }
    
    if (links.length > 0) {
      console.log(`🔗 Found ${links.length} gift links:`, links.slice(0, 10))
      
      // Пытаемся получить данные по первой ссылке
      const firstGift = await fetchGiftDetails(links[0])
      if (firstGift) {
        return [firstGift]
      }
    }

    console.log('❌ No gift data found in HTML')
    return null

  } catch (error) {
    console.error('💥 Error parsing Fragment page:', error)
    return null
  }
}

function parseGiftElements(elements) {
  const gifts = []
  
  elements.forEach((element, index) => {
    try {
      console.log(`🔍 Parsing element ${index + 1}...`)
      
      // Извлекаем данные из HTML элемента
      const nameMatch = element.match(/title="([^"]+)"|>([^<]+)</g)
      const priceMatch = element.match(/(\d+(?:\.\d+)?)\s*TON/i)
      const linkMatch = element.match(/href="\/gift\/([^"]+)"/i)
      
      if (nameMatch || priceMatch || linkMatch) {
        const gift = {
          id: linkMatch ? linkMatch[1] : `gift-${index}`,
          name: nameMatch ? nameMatch[0].replace(/[">]/g, '') : `Gift #${index}`,
          price: priceMatch ? parseFloat(priceMatch[1]) : 0,
          url: linkMatch ? `https://fragment.com/gift/${linkMatch[1]}` : null
        }
        
        console.log(`✅ Parsed gift:`, gift)
        gifts.push(gift)
      }
    } catch (e) {
      console.log(`❌ Failed to parse element ${index + 1}:`, e.message)
    }
  })
  
  return gifts
}

async function fetchGiftDetails(giftId) {
  try {
    console.log(`🔍 Fetching details for gift: ${giftId}`)
    
    const response = await fetch(`https://fragment.com/gift/${giftId}`, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    const html = await response.text()
    
    // Ищем данные о подарке
    const nameMatch = html.match(/<h1[^>]*>([^<]+)<\/h1>/i)
    const priceMatch = html.match(/(\d+(?:\.\d+)?)\s*TON/i)
    const descMatch = html.match(/<meta[^>]*name="description"[^>]*content="([^"]+)"/i)
    
    const gift = {
      id: giftId,
      name: nameMatch ? nameMatch[1] : `Gift ${giftId}`,
      price: priceMatch ? parseFloat(priceMatch[1]) : 0,
      description: descMatch ? descMatch[1] : '',
      url: `https://fragment.com/gift/${giftId}`
    }
    
    console.log(`✅ Gift details:`, gift)
    return gift
    
  } catch (error) {
    console.log(`❌ Failed to fetch gift ${giftId}:`, error.message)
    return null
  }
}

// Запускаем парсинг
parseFragmentGiftsPage().then(result => {
  if (result) {
    console.log('\n🎉 Successfully extracted Fragment data!')
    console.log('📊 Result:', result)
  } else {
    console.log('\n😞 Failed to extract Fragment data')
  }
}).catch(error => {
  console.error('💥 Test failed:', error)
})
