import { EventEmitter } from '../../events/event-emitter/EventEmitter.js';
import { BrowserNavigatorAnyHistoryItem, BrowserNavigatorConOptions, BrowserNavigatorEvents, BrowserNavigatorHashMode, BrowserNavigatorHistoryItem, URLLike } from './types.js';

type Emitter<State> = EventEmitter<BrowserNavigatorEvents<State>>;
export declare class BrowserNavigator<State = {}> {
    private readonly navigator;
    private readonly ee;
    readonly hashMode: BrowserNavigatorHashMode | null;
    readonly base: string;
    constructor(
    /**
     * Navigation history.
     */
    history: readonly BrowserNavigatorAnyHistoryItem<State>[], 
    /**
     * Currently active history item index.
     */
    index: number, { postEvent, hashMode, base }?: BrowserNavigatorConOptions);
    /**
     * Shows whether the navigator is currently attached to the browser history.
     */
    private attached;
    /**
     * Attaches current navigator to the browser history allowing navigator to manipulate it.
     */
    attach(): Promise<void>;
    /**
     * Goes back in history by 1.
     */
    back(): void;
    /**
     * Detaches current navigator from the browser history.
     */
    detach(): void;
    /**
     * Goes forward in history.
     */
    forward(): void;
    /**
     * Current history cursor.
     */
    get index(): number;
    /**
     * Current history item identifier.
     */
    get id(): string;
    /**
     * Changes currently active history item index by the specified delta. This method doesn't
     * change index in case, the updated index points to the non-existing history item. This behavior
     * is preserved until the `fit` argument is specified.
     * @param delta - index delta.
     * @param fit - cuts the delta argument to fit the bounds `[0, history.length - 1]`.
     */
    go(delta: number, fit?: boolean): void;
    /**
     * Goes to the specified index. Method does nothing in case, passed index is out of bounds.
     *
     * If "fit" option was specified and index is out of bounds, it will be cut to the nearest
     * bound.
     * @param index - target index.
     * @param fit - cuts the index argument to fit the bounds `[0, history.length - 1]`.
     */
    goTo(index: number, fit?: boolean): void;
    /**
     * Current history item hash.
     * @see URL.hash
     * @example
     * "", "#my-hash"
     */
    get hash(): string;
    /**
     * True if navigator has items before the current item.
     */
    get hasPrev(): boolean;
    /**
     * True if navigator has items after the current item.
     */
    get hasNext(): boolean;
    /**
     * Navigation history.
     */
    get history(): BrowserNavigatorHistoryItem<State>[];
    /**
     * Handles the window "popstate" event.
     * @param state - event state.
     */
    private onPopState;
    /**
     * Underlying navigator change event listener.
     */
    private onNavigatorChange;
    /**
     * Adds new event listener.
     */
    on: Emitter<State>['on'];
    /**
     * Removes event listener.
     */
    off: Emitter<State>['off'];
    /**
     * Path, including pathname, search and hash.
     * @example Pathname only.
     * "/pathname"
     * @example Pathname + search.
     * "/pathname?search"
     * @example Pathname + hash.
     * "/pathname#hash"
     * @example Pathname + search + hash.
     * "/pathname?search#hash"
     */
    get path(): string;
    /**
     * Current pathname. Always starts with the slash.
     * @see URL.pathname
     * @example
     * "/", "/abc"
     */
    get pathname(): string;
    /**
     * Depending on the current navigation type, parses incoming path and returns it presented as
     * an object. In other words, this method parses the passed path and returns object, describing
     * how the navigator "sees" it.
     *
     * @example Hash mode is omitted.
     * parsePath('/abc?a=1#hash');
     * // { pathname: '/abc', search: '?a=1', hash: '#hash' }
     * parsePath('http://example.com/abc?a=1#hash');
     * // { pathname: '/abc', search: '?a=1', hash: '#hash' }
     *
     * @example Hash mode is enabled.
     * parsePath('/abc?a=1#tma?is=cool#yeah');
     * // { pathname: '/tma', search: '?is=cool', hash: '#yeah' }
     * parsePath('http://example.com/abc?a=1#tma?is=cool#yeah');
     * // { pathname: '/tma', search: '?is=cool', hash: '#yeah' }
     */
    parsePath(path: string | URL): URLLike;
    /**
     * Pushes new history item. Method replaces all entries after the current one with the one
     * being pushed. Take a note, that passed item is always relative. In case, you want to use
     * it as an absolute one, use the "/" prefix. Example: "/absolute", { pathname: "/absolute" }.
     *
     * To create a final path, navigator uses a method, used in the URL class constructor, resolving
     * a path based on the current one.
     * @param path - entry path.
     * @param state - entry state.
     *
     * @example Pushing an absolute path.
     * push("/absolute"); // "/absolute"
     *
     * @example Pushing a relative path.
     * push("relative"); // "/home/<USER>" -> "/home/<USER>"
     *
     * @example Pushing query parameters.
     * push("/absolute?my-param=1"); // "/home/<USER>" -> "/absolute?my-param=1"
     * push("relative?my-param=1"); // "/home/<USER>" -> "/home/<USER>"
     * push("?my-param=1"); // "/home" -> "/home?my-param=1"
     *
     * @example Pushing hash.
     * push("#my-hash"); // "/home" -> "/home#my-hash"
     * push("relative#my-hash"); // "/home/<USER>" -> "/home/<USER>"
     *
     * @example Pushing state.
     * push("", { state: 'my-state' }); "/home/<USER>" -> "/home/<USER>"
     * push({ state: 'my-state' }); "/home/<USER>" -> "/home/<USER>"
     */
    push(path: string, state?: State): void;
    push(item: BrowserNavigatorAnyHistoryItem<State>): void;
    /**
     * Replaces the current history item. Has the same logic as the `push` method.
     * @param path - entry path.
     * @param state - entry state.
     * @see push
     */
    replace(path: string, state?: State): void;
    replace(item: BrowserNavigatorAnyHistoryItem<State>): void;
    /**
     * Combines the navigator `base` property with the passed path data applying the navigator
     * navigation mode.
     * @param value - path presented as string or URLLike.
     */
    renderPath(value: string | URLLike): string;
    /**
     * Synchronizes current navigator state with browser history.
     */
    private syncHistory;
    /**
     * Current query parameters.
     * @see URL.search
     * @example
     * "", "?", "?a=1"
     */
    get search(): string;
    /**
     * Current history item state.
     */
    get state(): State | undefined;
}
export {};
