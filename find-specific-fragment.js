// Поиск конкретных Fragment коллекций по известным адресам
console.log('🔍 Finding specific Fragment collections...')

const TON_API_KEY = '7125266008:c0ad27aa61ea792a52b4e7b39f9b1ed46751'
const TON_API_BASE = 'https://tonapi.io/v2'

// Известные адреса Fragment коллекций (из Fragment.com)
const KNOWN_FRAGMENT_ADDRESSES = [
  // Hex <PERSON>ts (уже проверен)
  '0:7a02d04f3ae4ed42697ca1203b5fc66a27942670e618aa8cfddac4185c6510af',
  
  // Попробуем найти другие через поиск по NFT
  // Будем искать NFT с fragment.com в метаданных
]

async function findFragmentCollectionsByNFT() {
  try {
    console.log('🚀 Searching Fragment collections by NFT metadata...')
    
    // Поиск NFT с fragment.com в изображениях
    const fragmentNFTs = []
    
    // Метод 1: Поиск через sales (NFT на продаже)
    console.log('\n💰 Method 1: Search through sales...')
    const salesData = await searchThroughSales()
    fragmentNFTs.push(...salesData)
    
    // Метод 2: Поиск через популярные коллекции
    console.log('\n📦 Method 2: Search through popular collections...')
    const popularData = await searchThroughPopularCollections()
    fragmentNFTs.push(...popularData)
    
    // Метод 3: Поиск по конкретным адресам
    console.log('\n🎯 Method 3: Check known addresses...')
    const knownData = await checkKnownAddresses()
    fragmentNFTs.push(...knownData)
    
    // Группируем по коллекциям
    const collectionsMap = new Map()
    
    fragmentNFTs.forEach(nft => {
      const collectionAddress = nft.collection?.address
      if (collectionAddress) {
        if (!collectionsMap.has(collectionAddress)) {
          collectionsMap.set(collectionAddress, {
            address: collectionAddress,
            name: nft.collection.name,
            description: nft.collection.description,
            nfts: []
          })
        }
        collectionsMap.get(collectionAddress).nfts.push(nft)
      }
    })
    
    const collections = Array.from(collectionsMap.values())
    
    console.log(`\n🎉 Found ${collections.length} Fragment collections:`)
    collections.forEach((collection, index) => {
      console.log(`\n${index + 1}. ${collection.name}`)
      console.log(`   Address: ${collection.address}`)
      console.log(`   NFTs found: ${collection.nfts.length}`)
      
      // Показываем примеры NFT
      collection.nfts.slice(0, 3).forEach((nft, nftIndex) => {
        console.log(`   ${nftIndex + 1}. ${nft.metadata?.name}`)
        if (nft.metadata?.image) {
          console.log(`      Image: ${nft.metadata.image}`)
        }
      })
    })
    
    return collections
    
  } catch (error) {
    console.error('💥 Error finding Fragment collections:', error)
    return []
  }
}

async function searchThroughSales() {
  try {
    const fragmentNFTs = []
    
    for (let page = 0; page < 5; page++) {
      const response = await fetch(`${TON_API_BASE}/nfts/sales?limit=50&offset=${page * 50}`, {
        headers: {
          'X-API-Key': TON_API_KEY,
          'Accept': 'application/json'
        }
      })

      if (!response.ok) break

      const data = await response.json()
      
      if (!data.sales || data.sales.length === 0) break
      
      const fragmentSales = data.sales.filter(sale => {
        const nft = sale.nft
        const image = nft?.metadata?.image || ''
        const lottie = nft?.metadata?.lottie || ''
        const name = nft?.metadata?.name || ''
        
        return image.includes('nft.fragment.com') || 
               lottie.includes('nft.fragment.com') ||
               name.includes('Hex Pot') ||
               name.includes('Loot Bag') ||
               name.includes('Plush Pepe') ||
               name.includes("Durov's Cap") ||
               name.includes('Ion Gem')
      })
      
      fragmentNFTs.push(...fragmentSales.map(sale => sale.nft))
      console.log(`  Page ${page + 1}: Found ${fragmentSales.length} Fragment NFTs`)
      
      await new Promise(resolve => setTimeout(resolve, 200))
    }
    
    console.log(`✅ Total Fragment NFTs from sales: ${fragmentNFTs.length}`)
    return fragmentNFTs
    
  } catch (error) {
    console.error('❌ Sales search failed:', error)
    return []
  }
}

async function searchThroughPopularCollections() {
  try {
    const fragmentNFTs = []
    
    // Получаем популярные коллекции
    const response = await fetch(`${TON_API_BASE}/nfts/collections?limit=100`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) return []

    const data = await response.json()
    
    for (const collection of data.nft_collections || []) {
      // Проверяем несколько NFT из каждой коллекции
      const nfts = await getCollectionNFTs(collection.address, 5)
      
      const fragmentNFTs_in_collection = nfts.filter(nft => {
        const image = nft.metadata?.image || ''
        const lottie = nft.metadata?.lottie || ''
        
        return image.includes('nft.fragment.com') || 
               lottie.includes('nft.fragment.com')
      })
      
      if (fragmentNFTs_in_collection.length > 0) {
        console.log(`  ✅ Found Fragment collection: ${collection.metadata?.name}`)
        fragmentNFTs.push(...fragmentNFTs_in_collection)
      }
      
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    console.log(`✅ Total Fragment NFTs from popular collections: ${fragmentNFTs.length}`)
    return fragmentNFTs
    
  } catch (error) {
    console.error('❌ Popular collections search failed:', error)
    return []
  }
}

async function checkKnownAddresses() {
  try {
    const fragmentNFTs = []
    
    for (const address of KNOWN_FRAGMENT_ADDRESSES) {
      console.log(`  🔍 Checking ${address}...`)
      
      const nfts = await getCollectionNFTs(address, 10)
      
      if (nfts.length > 0) {
        console.log(`    ✅ Found ${nfts.length} NFTs`)
        fragmentNFTs.push(...nfts)
      } else {
        console.log(`    ❌ No NFTs found`)
      }
      
      await new Promise(resolve => setTimeout(resolve, 200))
    }
    
    console.log(`✅ Total Fragment NFTs from known addresses: ${fragmentNFTs.length}`)
    return fragmentNFTs
    
  } catch (error) {
    console.error('❌ Known addresses check failed:', error)
    return []
  }
}

async function getCollectionNFTs(collectionAddress, limit = 10) {
  try {
    const response = await fetch(`${TON_API_BASE}/nfts/collections/${collectionAddress}/items?limit=${limit}`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) return []

    const data = await response.json()
    return data.nft_items || []
  } catch (error) {
    return []
  }
}

// Запускаем поиск
findFragmentCollectionsByNFT().then(collections => {
  console.log('\n🎉 Fragment collection search completed!')
  
  if (collections.length > 0) {
    console.log('\n💻 Code for integration:')
    console.log('const FRAGMENT_COLLECTIONS = {')
    collections.forEach(collection => {
      const key = collection.name?.toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .substring(0, 20) || 'unknown'
      console.log(`  '${key}': '${collection.address}',`)
    })
    console.log('}')
    
    // Статистика
    const totalNFTs = collections.reduce((sum, col) => sum + col.nfts.length, 0)
    console.log(`\n📈 Statistics:`)
    console.log(`  Fragment Collections: ${collections.length}`)
    console.log(`  Total Fragment NFTs: ${totalNFTs}`)
    console.log(`  Average per collection: ${Math.round(totalNFTs / collections.length)}`)
    
    console.log('\n🚀 Ready to integrate all Fragment collections!')
  } else {
    console.log('\n😞 No Fragment collections found')
    console.log('💡 Will use fallback method with HTML parsing')
  }
}).catch(error => {
  console.error('💥 Search failed:', error)
})
