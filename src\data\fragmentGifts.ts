import { NFTGift } from '../types/nft'

// Реальные коллекции подарков из Fragment с актуальными ценами
export const fragmentCollections = [
  {
    id: 'plushpepe',
    name: 'Plush Pepes',
    description: 'Rare collectible Pepe plushies',
    icon: '🐸',
    totalItems: 1489,
    floorPrice: 1200,
    topPrice: 4999
  },
  {
    id: 'durovscap',
    name: "<PERSON><PERSON>'s Caps",
    description: 'Exclusive caps from <PERSON> Durov',
    icon: '🧢',
    totalItems: 2082,
    floorPrice: 1199,
    topPrice: 5999
  },
  {
    id: 'lootbag',
    name: 'Loot Bags',
    description: 'Mysterious bags with hidden treasures',
    icon: '💰',
    totalItems: 2951,
    floorPrice: 500,
    topPrice: 11111
  },
  {
    id: 'iongem',
    name: 'Ion Gems',
    description: 'Powerful crystalline energy sources',
    icon: '💎',
    totalItems: 1252,
    floorPrice: 800,
    topPrice: 5000
  },
  {
    id: 'witchhat',
    name: 'Witch Hats',
    description: 'Magical hats with mystical powers',
    icon: '🎩',
    totalItems: 25128,
    floorPrice: 50,
    topPrice: 800
  },
  {
    id: 'spyagaric',
    name: '<PERSON> Agarics',
    description: 'Surveillance mushrooms for secret operations',
    icon: '🍄',
    totalItems: 22575,
    floorPrice: 30,
    topPrice: 500
  },
  {
    id: 'homemadecake',
    name: 'Homemade Cakes',
    description: 'Delicious cakes baked with love',
    icon: '🎂',
    totalItems: 18217,
    floorPrice: 25,
    topPrice: 300
  },
  {
    id: 'deskcalendar',
    name: 'Desk Calendars',
    description: 'Vintage calendars for time management',
    icon: '📅',
    totalItems: 12553,
    floorPrice: 20,
    topPrice: 250
  },
  {
    id: 'evileye',
    name: 'Evil Eyes',
    description: 'Protective amulets against bad luck',
    icon: '🧿',
    totalItems: 11704,
    floorPrice: 40,
    topPrice: 400
  },
  {
    id: 'hexpot',
    name: 'Hex Pots',
    description: 'Magical cauldrons for brewing potions',
    icon: '🪄',
    totalItems: 9611,
    floorPrice: 60,
    topPrice: 600
  }
]

export const generateRandomGift = (collectionId?: string): NFTGift => {
  const collections = collectionId 
    ? fragmentCollections.filter(c => c.id === collectionId)
    : fragmentCollections

  const collection = collections[Math.floor(Math.random() * collections.length)]
  const giftNumber = Math.floor(Math.random() * collection.totalItems) + 1
  
  // Определяем редкость на основе номера
  let rarity: NFTGift['rarity']
  let priceMultiplier: number
  
  if (giftNumber <= 100) {
    rarity = 'legendary'
    priceMultiplier = 0.8 + Math.random() * 0.4 // 80-120% от топ цены
  } else if (giftNumber <= 500) {
    rarity = 'epic'
    priceMultiplier = 0.4 + Math.random() * 0.4 // 40-80% от топ цены
  } else if (giftNumber <= 2000) {
    rarity = 'rare'
    priceMultiplier = 0.2 + Math.random() * 0.3 // 20-50% от топ цены
  } else {
    rarity = 'common'
    priceMultiplier = 0.05 + Math.random() * 0.2 // 5-25% от топ цены
  }

  const basePrice = collection.floorPrice + (collection.topPrice - collection.floorPrice) * priceMultiplier
  const finalPrice = Math.round(basePrice * (0.8 + Math.random() * 0.4)) // ±20% вариация

  const rarityColors = {
    common: '#9CA3AF',
    rare: '#3B82F6', 
    epic: '#8B5CF6',
    legendary: '#F59E0B'
  }

  return {
    id: `${collection.id}-${giftNumber}`,
    name: `${collection.name.slice(0, -1)} #${giftNumber}`,
    description: `${collection.description} - ${rarity} collectible from Telegram`,
    collection: collection.name,
    image: `https://nft.fragment.com/gift/${collection.id}-${giftNumber}.medium.jpg`,
    lottieUrl: `https://nft.fragment.com/gift/${collection.id}-${giftNumber}.lottie.json`,
    rarity,
    attributes: {
      background: rarityColors[rarity],
      icon: collection.icon,
      luckyNumber: giftNumber,
      pattern: rarity === 'legendary' ? 'golden' : rarity === 'epic' ? 'mystical' : 'standard',
      effect: rarity === 'legendary' ? 'divine' : rarity === 'epic' ? 'magical' : 'normal'
    },
    value: finalPrice / 1000, // Конвертируем в TON (предполагая 1000 = 1 TON)
    isOwned: true,
    dateReceived: new Date(),
    fragmentUrl: `https://fragment.com/gift/${collection.id}-${giftNumber}`
  }
}

// Предустановленные подарки для демонстрации
export const sampleGifts: NFTGift[] = [
  {
    id: 'plushpepe-1337',
    name: 'Plush Pepe #1337',
    description: 'Elite hacker Pepe with legendary status',
    collection: 'Plush Pepes',
    image: 'https://nft.fragment.com/gift/plushpepe-1337.medium.jpg',
    lottieUrl: 'https://nft.fragment.com/gift/plushpepe-1337.lottie.json',
    rarity: 'legendary',
    attributes: {
      background: '#F59E0B',
      icon: '🐸',
      luckyNumber: 1337,
      pattern: 'golden',
      effect: 'divine'
    },
    value: 2.5,
    isOwned: true,
    dateReceived: new Date(Date.now() - 24 * 60 * 60 * 1000),
    fragmentUrl: 'https://fragment.com/gift/plushpepe-1337'
  },
  {
    id: 'durovscap-96',
    name: "Durov's Cap #96",
    description: 'Rare cap worn by Pavel Durov himself',
    collection: "Durov's Caps",
    image: 'https://nft.fragment.com/gift/durovscap-96.medium.jpg',
    lottieUrl: 'https://nft.fragment.com/gift/durovscap-96.lottie.json',
    rarity: 'epic',
    attributes: {
      background: '#8B5CF6',
      icon: '🧢',
      luckyNumber: 96,
      pattern: 'mystical',
      effect: 'magical'
    },
    value: 2.096,
    isOwned: true,
    dateReceived: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    fragmentUrl: 'https://fragment.com/gift/durovscap-96'
  },
  {
    id: 'witchhat-13',
    name: 'Witch Hat #13',
    description: 'Unlucky number brings magical luck',
    collection: 'Witch Hats',
    image: 'https://nft.fragment.com/gift/witchhat-13.medium.jpg',
    lottieUrl: 'https://nft.fragment.com/gift/witchhat-13.lottie.json',
    rarity: 'rare',
    attributes: {
      background: '#3B82F6',
      icon: '🎩',
      luckyNumber: 13,
      pattern: 'mystical',
      effect: 'magical'
    },
    value: 0.4,
    isOwned: true,
    dateReceived: new Date(Date.now() - 3 * 60 * 60 * 1000),
    fragmentUrl: 'https://fragment.com/gift/witchhat-13'
  }
]
