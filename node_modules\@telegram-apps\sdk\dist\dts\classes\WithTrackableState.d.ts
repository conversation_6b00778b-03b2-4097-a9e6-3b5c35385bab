import { WithStateUtils } from './WithStateUtils.js';
import { StateEvents } from './State/types.js';
import { EventEmitter } from '../events/event-emitter/EventEmitter.js';

type Emitter<StateShape extends object> = EventEmitter<StateEvents<StateShape>>;
export declare class WithTrackableState<StateShape extends object> extends WithStateUtils<StateShape> {
    /**
     * Adds a new event listener.
     */
    on: Emitter<StateShape>['on'];
    /**
     * Removes the event listener.
     */
    off: Emitter<StateShape>['off'];
}
export {};
