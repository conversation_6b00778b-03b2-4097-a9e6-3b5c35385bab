const TelegramBot = require('node-telegram-bot-api')
const express = require('express')
const cors = require('cors')
const dotenv = require('dotenv')

dotenv.config()

const token = process.env.BOT_TOKEN
const webAppUrl = process.env.WEBAPP_URL || 'https://your-domain.com'
const port = process.env.PORT || 3000

// Create bot instance
const bot = new TelegramBot(token, { polling: true })

// Create Express app for webhook and API
const app = express()
app.use(cors())
app.use(express.json())

// Bot commands and handlers
bot.onText(/\/start/, (msg) => {
  const chatId = msg.chat.id
  const user = msg.from

  const welcomeMessage = `🎁 Welcome to NFT Gifts, ${user.first_name}!

Discover, collect, and trade exclusive NFT gifts on the TON blockchain.

✨ Features:
• Open treasure chests with different rarities
• Participate in weekly lottery draws
• Collect unique animated NFT gifts
• Trade on integrated marketplaces

Ready to start your collection?`

  const keyboard = {
    inline_keyboard: [
      [
        {
          text: '🎮 Open App',
          web_app: { url: webAppUrl }
        }
      ],
      [
        {
          text: '📖 How it Works',
          callback_data: 'how_it_works'
        },
        {
          text: '🏆 Leaderboard',
          callback_data: 'leaderboard'
        }
      ],
      [
        {
          text: '💎 Marketplace',
          url: 'https://fragment.com'
        }
      ]
    ]
  }

  bot.sendMessage(chatId, welcomeMessage, {
    reply_markup: keyboard,
    parse_mode: 'HTML'
  })
})

bot.onText(/\/help/, (msg) => {
  const chatId = msg.chat.id

  const helpMessage = `🆘 <b>NFT Gifts Help</b>

<b>Commands:</b>
/start - Start the bot and open the app
/help - Show this help message
/stats - View your collection statistics
/marketplace - Open NFT marketplace

<b>How to Play:</b>
1. Open the Mini App
2. Buy treasure chests with Stars or TON
3. Open chests to get random NFT gifts
4. Participate in lottery draws
5. Collect and trade your NFTs

<b>Chest Types:</b>
🎁 Demo Chest - Free practice (higher chances)
📦 Bronze Chest - 50 Stars (common rewards)
🎀 Silver Chest - 150 Stars (better odds)
💎 Gold Chest - 0.1 TON (rare NFTs)

<b>Lottery:</b>
• Buy tickets with Stars or TON
• Complete tasks to earn free tickets
• Win exclusive NFT prizes weekly

<b>Support:</b>
If you need help, contact @support`

  bot.sendMessage(chatId, helpMessage, {
    parse_mode: 'HTML',
    reply_markup: {
      inline_keyboard: [
        [
          {
            text: '🎮 Open App',
            web_app: { url: webAppUrl }
          }
        ]
      ]
    }
  })
})

bot.onText(/\/stats/, (msg) => {
  const chatId = msg.chat.id
  const userId = msg.from.id

  // Mock user stats - in real app, fetch from database
  const stats = {
    chestsOpened: 15,
    nftsCollected: 12,
    totalValue: 0.85,
    favoriteRarity: 'Rare',
    lotteryWins: 1
  }

  const statsMessage = `📊 <b>Your Collection Stats</b>

🎁 Chests Opened: ${stats.chestsOpened}
💎 NFTs Collected: ${stats.nftsCollected}
💰 Total Value: ${stats.totalValue} TON
⭐ Favorite Rarity: ${stats.favoriteRarity}
🏆 Lottery Wins: ${stats.lotteryWins}

Keep collecting to increase your stats!`

  bot.sendMessage(chatId, statsMessage, {
    parse_mode: 'HTML',
    reply_markup: {
      inline_keyboard: [
        [
          {
            text: '🎮 Open App',
            web_app: { url: webAppUrl }
          },
          {
            text: '🖼️ View Gallery',
            web_app: { url: `${webAppUrl}#gallery` }
          }
        ]
      ]
    }
  })
})

// Handle callback queries
bot.on('callback_query', (callbackQuery) => {
  const message = callbackQuery.message
  const data = callbackQuery.data
  const chatId = message.chat.id

  switch (data) {
    case 'how_it_works':
      const howItWorksMessage = `📖 <b>How NFT Gifts Works</b>

<b>1. Treasure Chests 🎁</b>
Open chests to get random NFT gifts. Higher tier chests have better rewards but cost more.

<b>2. Rarity System ⭐</b>
• Common (70%) - Basic gifts
• Rare (25%) - Better designs
• Epic (4%) - Special effects
• Legendary (1%) - Ultra rare

<b>3. Lottery System 🎰</b>
Buy tickets to participate in weekly draws for exclusive NFTs.

<b>4. NFT Attributes 🎨</b>
Each NFT has unique:
• Background color
• Icon design
• Lucky number
• Special effects

<b>5. Blockchain Integration ⛓️</b>
All NFTs are minted on TON blockchain and can be traded on marketplaces.`

      bot.editMessageText(howItWorksMessage, {
        chat_id: chatId,
        message_id: message.message_id,
        parse_mode: 'HTML',
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: '🎮 Open App',
                web_app: { url: webAppUrl }
              }
            ],
            [
              {
                text: '← Back',
                callback_data: 'back_to_start'
              }
            ]
          ]
        }
      })
      break

    case 'leaderboard':
      const leaderboardMessage = `🏆 <b>Top Collectors</b>

1. 👑 Alice - 45 NFTs (12.5 TON)
2. 🥈 Bob - 38 NFTs (9.2 TON)
3. 🥉 Charlie - 32 NFTs (7.8 TON)
4. 4️⃣ Diana - 28 NFTs (6.1 TON)
5. 5️⃣ Eve - 25 NFTs (5.4 TON)

Your rank: #47 (12 NFTs, 0.85 TON)

Keep collecting to climb the leaderboard!`

      bot.editMessageText(leaderboardMessage, {
        chat_id: chatId,
        message_id: message.message_id,
        parse_mode: 'HTML',
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: '🎮 Open App',
                web_app: { url: webAppUrl }
              }
            ],
            [
              {
                text: '← Back',
                callback_data: 'back_to_start'
              }
            ]
          ]
        }
      })
      break

    case 'back_to_start':
      // Resend the start message
      bot.deleteMessage(chatId, message.message_id)
      bot.sendMessage(chatId, '/start')
      break
  }

  bot.answerCallbackQuery(callbackQuery.id)
})

// Handle web app data
bot.on('message', (msg) => {
  if (msg.web_app_data) {
    const chatId = msg.chat.id
    const data = JSON.parse(msg.web_app_data.data)

    console.log('Received web app data:', data)

    switch (data.type) {
      case 'chest_opened':
        bot.sendMessage(chatId, `🎉 Congratulations! You opened a ${data.chestType} and received: ${data.reward.name}!`, {
          reply_markup: {
            inline_keyboard: [
              [
                {
                  text: '🎮 Open Another',
                  web_app: { url: webAppUrl }
                }
              ]
            ]
          }
        })
        break

      case 'lottery_ticket_bought':
        bot.sendMessage(chatId, `🎫 Lottery ticket purchased! Your ticket number: #${data.ticketNumber}`)
        break

      case 'share_nft':
        const nft = data.nft
        bot.sendMessage(chatId, `🎁 Check out my new NFT: ${nft.name}!\n\n${nft.description}\n\nRarity: ${nft.rarity}\nValue: ${nft.value} TON\n\nGet yours at @YourBotUsername`)
        break
    }
  }
})

// Express routes for API
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() })
})

app.get('/api/user/:userId/stats', (req, res) => {
  const userId = req.params.userId

  // Mock user stats - in real app, fetch from database
  const stats = {
    userId,
    chestsOpened: 15,
    nftsCollected: 12,
    totalValue: 0.85,
    favoriteRarity: 'rare',
    lotteryWins: 1,
    balance: {
      stars: 100,
      ton: 0.5,
      tickets: 3
    }
  }

  res.json(stats)
})

app.post('/api/chest/open', (req, res) => {
  const { userId, chestType } = req.body

  // Mock chest opening logic
  const rewards = {
    demo: { rarity: 'common', chance: 80 },
    bronze: { rarity: 'common', chance: 70 },
    silver: { rarity: 'rare', chance: 35 },
    gold: { rarity: 'epic', chance: 25 }
  }

  const reward = {
    id: `nft_${Date.now()}`,
    name: `${chestType} Gift`,
    rarity: rewards[chestType]?.rarity || 'common',
    value: Math.random() * 0.1
  }

  res.json({ success: true, reward })
})

// Start the server
app.listen(port, () => {
  console.log(`🤖 Bot server running on port ${port}`)
  console.log(`📱 Web App URL: ${webAppUrl}`)
})

console.log('🚀 NFT Gifts Bot started successfully!')
console.log('📋 Available commands:')
console.log('  /start - Start the bot')
console.log('  /help - Show help')
console.log('  /stats - Show user stats')
