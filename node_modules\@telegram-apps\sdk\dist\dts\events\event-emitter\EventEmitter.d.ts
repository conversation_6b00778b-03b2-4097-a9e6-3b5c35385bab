import { RemoveEventListenerFn } from '../types.js';
import { EmptyEventName, EventListener, EventName, EventParams, NonEmptyEventName, SubscribeListener } from './types.js';

export declare class EventEmitter<Schema> {
    private readonly listeners;
    private listenersCount;
    private subscribeListeners;
    /**
     * Removes all event listeners.
     */
    clear(): void;
    /**
     * Returns count of bound listeners.
     */
    get count(): number;
    /**
     * Emits known event which has no parameters.
     * @param event - event name.
     */
    emit<E extends EmptyEventName<Schema>>(event: E): void;
    /**
     * Emits known event which has parameters.
     * @param event - event name.
     * @param args - list of event listener arguments.
     */
    emit<E extends NonEmptyEventName<Schema>>(event: E, ...args: EventParams<Schema[E]>): void;
    /**
     * Adds new event listener.
     * @param event - event name.
     * @param listener - event listener.
     * @param once - should listener be called only once.
     * @returns Function to remove bound event listener.
     */
    on<E extends EventName<Schema>>(event: E, listener: EventListener<Schema[E]>, once?: boolean): RemoveEventListenerFn;
    /**
     * Removes event listener. In case, specified listener was bound several times, it removes
     * only a single one.
     * @param event - event name.
     * @param listener - event listener.
     */
    off<E extends EventName<Schema>>(event: E, listener: EventListener<Schema[E]>): void;
    /**
     * Adds a new event listener for all events.
     * @param listener - event listener.
     * @returns Function to remove event listener.
     */
    subscribe(listener: SubscribeListener<Schema>): RemoveEventListenerFn;
    /**
     * Removes global event listener. In case, specified listener was bound several times, it removes
     * only a single one.
     * @param listener - event listener.
     */
    unsubscribe(listener: SubscribeListener<Schema>): void;
}
