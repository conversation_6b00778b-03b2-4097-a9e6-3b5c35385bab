// Тестирование TON API для получения реальных данных Fragment NFT
console.log('🔍 Testing TON API for real Fragment NFT data...')

const TON_API_KEY = '7125266008:c0ad27aa61ea792a52b4e7b39f9b1ed46751'
const TON_API_BASE = 'https://tonapi.io/v2'

// Пример адреса Fragment NFT из вашего примера
const EXAMPLE_NFT_ADDRESS = 'UQA6T1et7YW-j30WUbjp13SWYgFOCPa806-vIXXIMYin2kxx'
const FRAGMENT_COLLECTION = 'UQB6AtBPOuTtQml8oSA7X8ZqJ5QmcOYYqoz92sQYXGUQr0FE'

async function testTonAPI() {
  try {
    console.log('🚀 Testing TON API connection...')
    
    // Тест 1: Получить информацию о конкретном NFT
    console.log('\n📦 Test 1: Get specific NFT info...')
    const nftInfo = await getNFTInfo(EXAMPLE_NFT_ADDRESS)
    
    // Тест 2: Получить коллекцию Fragment
    console.log('\n📚 Test 2: Get Fragment collection...')
    const collectionInfo = await getCollectionInfo(FRAGMENT_COLLECTION)
    
    // Тест 3: Получить все NFT из коллекции
    console.log('\n🎁 Test 3: Get NFTs from collection...')
    const collectionNFTs = await getCollectionNFTs(FRAGMENT_COLLECTION, 20)
    
    // Тест 4: Поиск Fragment коллекций
    console.log('\n🔍 Test 4: Search Fragment collections...')
    const fragmentCollections = await searchFragmentCollections()
    
    // Тест 5: Получить цены с маркетплейса
    console.log('\n💰 Test 5: Get marketplace prices...')
    const marketplacePrices = await getMarketplacePrices()
    
    return {
      nftInfo,
      collectionInfo,
      collectionNFTs,
      fragmentCollections,
      marketplacePrices
    }
    
  } catch (error) {
    console.error('💥 TON API test failed:', error)
    return null
  }
}

async function getNFTInfo(address) {
  try {
    console.log(`🔍 Getting NFT info for: ${address}`)
    
    const response = await fetch(`${TON_API_BASE}/nfts/${address}`, {
      headers: {
        'Authorization': `Bearer ${TON_API_KEY}`,
        'Accept': 'application/json'
      }
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    console.log('✅ NFT Info:', data)
    
    // Извлекаем важную информацию
    const nftData = {
      address: data.address,
      name: data.metadata?.name,
      description: data.metadata?.description,
      image: data.metadata?.image,
      attributes: data.metadata?.attributes,
      collection: data.collection?.address,
      owner: data.owner?.address,
      sale: data.sale ? {
        price: data.sale.price?.value,
        currency: data.sale.price?.token_name,
        marketplace: data.sale.marketplace
      } : null
    }
    
    console.log('📊 Parsed NFT data:', nftData)
    return nftData
    
  } catch (error) {
    console.error(`❌ Failed to get NFT info: ${error.message}`)
    return null
  }
}

async function getCollectionInfo(address) {
  try {
    console.log(`🔍 Getting collection info for: ${address}`)
    
    const response = await fetch(`${TON_API_BASE}/nfts/collections/${address}`, {
      headers: {
        'Authorization': `Bearer ${TON_API_KEY}`,
        'Accept': 'application/json'
      }
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    console.log('✅ Collection Info:', data)
    
    return {
      address: data.address,
      name: data.metadata?.name,
      description: data.metadata?.description,
      image: data.metadata?.image,
      total_items: data.next_item_index,
      owner: data.owner?.address
    }
    
  } catch (error) {
    console.error(`❌ Failed to get collection info: ${error.message}`)
    return null
  }
}

async function getCollectionNFTs(collectionAddress, limit = 20) {
  try {
    console.log(`🔍 Getting NFTs from collection: ${collectionAddress}`)
    
    const response = await fetch(`${TON_API_BASE}/nfts/collections/${collectionAddress}/items?limit=${limit}`, {
      headers: {
        'Authorization': `Bearer ${TON_API_KEY}`,
        'Accept': 'application/json'
      }
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    console.log(`✅ Found ${data.nft_items?.length || 0} NFTs in collection`)
    
    if (data.nft_items && data.nft_items.length > 0) {
      // Показываем первые 5 NFT
      console.log('🎁 Sample NFTs:')
      data.nft_items.slice(0, 5).forEach((nft, index) => {
        console.log(`  ${index + 1}. ${nft.metadata?.name || 'Unknown'} - ${nft.address}`)
        if (nft.sale) {
          console.log(`     💰 Price: ${nft.sale.price?.value} ${nft.sale.price?.token_name}`)
        }
      })
      
      return data.nft_items.map(nft => ({
        address: nft.address,
        name: nft.metadata?.name,
        description: nft.metadata?.description,
        image: nft.metadata?.image,
        attributes: nft.metadata?.attributes,
        owner: nft.owner?.address,
        sale: nft.sale ? {
          price: nft.sale.price?.value,
          currency: nft.sale.price?.token_name,
          marketplace: nft.sale.marketplace
        } : null
      }))
    }
    
    return []
    
  } catch (error) {
    console.error(`❌ Failed to get collection NFTs: ${error.message}`)
    return []
  }
}

async function searchFragmentCollections() {
  try {
    console.log('🔍 Searching for Fragment collections...')
    
    // Поиск коллекций по ключевым словам
    const searchTerms = ['fragment', 'gift', 'loot', 'pepe', 'durov']
    const collections = []
    
    for (const term of searchTerms) {
      try {
        const response = await fetch(`${TON_API_BASE}/nfts/collections?limit=10`, {
          headers: {
            'Authorization': `Bearer ${TON_API_KEY}`,
            'Accept': 'application/json'
          }
        })
        
        if (response.ok) {
          const data = await response.json()
          if (data.nft_collections) {
            const filtered = data.nft_collections.filter(collection => 
              collection.metadata?.name?.toLowerCase().includes(term) ||
              collection.metadata?.description?.toLowerCase().includes(term)
            )
            collections.push(...filtered)
          }
        }
      } catch (e) {
        console.log(`❌ Search for "${term}" failed:`, e.message)
      }
    }
    
    console.log(`✅ Found ${collections.length} potential Fragment collections`)
    return collections
    
  } catch (error) {
    console.error(`❌ Failed to search collections: ${error.message}`)
    return []
  }
}

async function getMarketplacePrices() {
  try {
    console.log('🔍 Getting marketplace prices...')
    
    // Получаем NFT которые продаются
    const response = await fetch(`${TON_API_BASE}/nfts/sales?limit=50`, {
      headers: {
        'Authorization': `Bearer ${TON_API_KEY}`,
        'Accept': 'application/json'
      }
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    console.log(`✅ Found ${data.sales?.length || 0} NFTs for sale`)
    
    if (data.sales && data.sales.length > 0) {
      const prices = data.sales
        .filter(sale => sale.price?.token_name === 'TON')
        .map(sale => ({
          nft: sale.nft?.metadata?.name || 'Unknown',
          price: parseFloat(sale.price.value) / 1000000000, // Конвертируем из nanoTON
          address: sale.nft?.address,
          marketplace: sale.marketplace
        }))
        .sort((a, b) => b.price - a.price)
      
      console.log('💰 Top prices:')
      prices.slice(0, 10).forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.nft}: ${item.price.toFixed(3)} TON`)
      })
      
      return prices
    }
    
    return []
    
  } catch (error) {
    console.error(`❌ Failed to get marketplace prices: ${error.message}`)
    return []
  }
}

// Запускаем тесты
testTonAPI().then(results => {
  if (results) {
    console.log('\n🎉 TON API tests completed successfully!')
    console.log('\n📊 Summary:')
    console.log(`✅ NFT Info: ${results.nftInfo ? 'Success' : 'Failed'}`)
    console.log(`✅ Collection Info: ${results.collectionInfo ? 'Success' : 'Failed'}`)
    console.log(`✅ Collection NFTs: ${results.collectionNFTs?.length || 0} items`)
    console.log(`✅ Fragment Collections: ${results.fragmentCollections?.length || 0} found`)
    console.log(`✅ Marketplace Prices: ${results.marketplacePrices?.length || 0} items`)
    
    if (results.marketplacePrices && results.marketplacePrices.length > 0) {
      const prices = results.marketplacePrices.map(p => p.price)
      console.log(`\n💰 Price range: ${Math.min(...prices).toFixed(3)} - ${Math.max(...prices).toFixed(3)} TON`)
    }
  } else {
    console.log('\n😞 TON API tests failed')
  }
}).catch(error => {
  console.error('💥 Test error:', error)
})
