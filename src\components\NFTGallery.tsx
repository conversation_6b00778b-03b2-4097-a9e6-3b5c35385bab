import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Filter, Search, ExternalLink, Share2, <PERSON><PERSON><PERSON>, Loader2, RefreshCw } from 'lucide-react'
import { NFTGift } from '../types/nft'
import { useTelegramWebApp } from '../hooks/useTelegramWebApp'
import { FragmentAPI } from '../services/fragmentApi'
import LottieAnimation from './LottieAnimation'

const NFTGallery = () => {
  const [nfts, setNfts] = useState<NFTGift[]>([])
  const [filteredNfts, setFilteredNfts] = useState<NFTGift[]>([])
  const [selectedRarity, setSelectedRarity] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedNft, setSelectedNft] = useState<NFTGift | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const { hapticFeedback } = useTelegramWebApp()

  // Load NFT collection from Fragment API
  useEffect(() => {
    loadNFTs()
  }, [])

  const loadNFTs = async () => {
    try {
      setIsLoading(true)

      // Load multiple random NFTs from different collections with real-time prices
      const collections = await FragmentAPI.getCollections()
      const promises = []

      // Get 2-4 random NFTs from each collection
      for (const collection of collections.slice(0, 8)) {
        const nftCount = Math.floor(Math.random() * 3) + 2
        for (let i = 0; i < nftCount; i++) {
          promises.push(FragmentAPI.getRandomGift(collection.id))
        }
      }

      const loadedNfts = await Promise.all(promises)

      // Sort by value (highest first) to show most valuable NFTs
      loadedNfts.sort((a, b) => b.value - a.value)

      setNfts(loadedNfts)
      setFilteredNfts(loadedNfts)
    } catch (error) {
      console.error('Failed to load NFTs:', error)
      // Fallback to sample data
      const fallbackNfts = await Promise.all([
        FragmentAPI.getRandomGift(),
        FragmentAPI.getRandomGift(),
        FragmentAPI.getRandomGift(),
        FragmentAPI.getRandomGift(),
        FragmentAPI.getRandomGift()
      ])
      setNfts(fallbackNfts)
      setFilteredNfts(fallbackNfts)
    } finally {
      setIsLoading(false)
    }
  }

  const refreshNFTs = async () => {
    setIsRefreshing(true)
    hapticFeedback.impact('light')
    await loadNFTs()
    setIsRefreshing(false)
  }

  // Filter NFTs
  useEffect(() => {
    let filtered = nfts

    if (selectedRarity !== 'all') {
      filtered = filtered.filter(nft => nft.rarity === selectedRarity)
    }

    if (searchQuery) {
      filtered = filtered.filter(nft =>
        nft.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        nft.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        nft.collection.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    setFilteredNfts(filtered)
  }, [nfts, selectedRarity, searchQuery])

  const rarityColors = {
    common: 'border-gray-400 bg-gray-50',
    rare: 'border-blue-400 bg-blue-50',
    epic: 'border-purple-400 bg-purple-50',
    legendary: 'border-yellow-400 bg-yellow-50'
  }

  const rarityLabels = {
    common: 'Common',
    rare: 'Rare',
    epic: 'Epic',
    legendary: 'Legendary'
  }

  const handleNftClick = (nft: NFTGift) => {
    hapticFeedback.selection()
    setSelectedNft(nft)
  }

  const handleShare = (nft: NFTGift) => {
    hapticFeedback.impact('light')
    if (navigator.share) {
      navigator.share({
        title: nft.name,
        text: nft.description,
        url: window.location.href
      })
    }
  }

  const handleViewOnMarketplace = (nft: NFTGift) => {
    hapticFeedback.impact('light')
    // Open Fragment marketplace for this specific NFT
    const url = nft.fragmentUrl || 'https://fragment.com/gifts'
    window.open(url, '_blank')
  }

  const totalValue = nfts.reduce((sum, nft) => sum + nft.value, 0)

  return (
    <div className="space-y-6">
      {/* Collection Stats */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-bold flex items-center space-x-2">
              <span>My NFT Collection</span>
              <div className="flex items-center space-x-1 text-xs bg-green-500/20 px-2 py-1 rounded-full">
                <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse" />
                <span className="text-green-400 font-medium">Live Prices</span>
              </div>
            </h2>
          </div>
          <motion.button
            whileTap={{ scale: 0.95 }}
            onClick={refreshNFTs}
            disabled={isRefreshing}
            className="p-2 rounded-xl bg-blue-500/20 border border-blue-400/30 hover:bg-blue-500/30 transition-colors disabled:opacity-50"
            title="Refresh with latest Fragment prices"
          >
            <RefreshCw className={`w-5 h-5 text-blue-400 ${isRefreshing ? 'animate-spin' : ''}`} />
          </motion.button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-8 h-8 text-blue-400 animate-spin" />
            <span className="ml-3 text-tg-hint">Loading NFTs with live Fragment prices...</span>
          </div>
        ) : (
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold">{nfts.length}</p>
              <p className="text-sm text-tg-hint">NFTs Owned</p>
            </div>
            <div>
              <p className="text-2xl font-bold">{totalValue.toFixed(3)}</p>
              <p className="text-sm text-tg-hint">Total Value (TON)</p>
            </div>
            <div>
              <p className="text-2xl font-bold">
                {new Set(nfts.map(nft => nft.rarity)).size}
              </p>
              <p className="text-sm text-tg-hint">Rarities</p>
            </div>
          </div>
        )}
      </div>

      {/* Search and Filter */}
      <div className="space-y-3">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-tg-hint" />
          <input
            type="text"
            placeholder="Search NFTs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 bg-tg-secondary-bg rounded-xl border-none outline-none text-tg-text placeholder-tg-hint"
          />
        </div>

        {/* Rarity Filter */}
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-tg-hint" />
          <div className="flex space-x-2 overflow-x-auto">
            <button
              onClick={() => setSelectedRarity('all')}
              className={`px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap ${
                selectedRarity === 'all'
                  ? 'bg-tg-button text-tg-button-text'
                  : 'bg-tg-secondary-bg text-tg-hint'
              }`}
            >
              All
            </button>
            {Object.entries(rarityLabels).map(([rarity, label]) => (
              <button
                key={rarity}
                onClick={() => setSelectedRarity(rarity)}
                className={`px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap ${
                  selectedRarity === rarity
                    ? 'bg-tg-button text-tg-button-text'
                    : 'bg-tg-secondary-bg text-tg-hint'
                }`}
              >
                {label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* NFT Grid */}
      {filteredNfts.length > 0 ? (
        <div className="grid grid-cols-2 gap-4">
          {filteredNfts.map((nft, index) => (
            <motion.div
              key={nft.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`nft-card rarity-glow rarity-${nft.rarity} card cursor-pointer border-2 ${rarityColors[nft.rarity]}`}
              onClick={() => handleNftClick(nft)}
            >
              <div className="space-y-3">
                {/* NFT Image/Animation */}
                <div className="relative aspect-square bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden">
                  {nft.lottieUrl ? (
                    <LottieAnimation url={nft.lottieUrl} />
                  ) : (
                    <img
                      src={nft.image}
                      alt={nft.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        // Fallback to collection icon if image fails
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        const parent = target.parentElement
                        if (parent) {
                          parent.innerHTML = `
                            <div class="w-full h-full flex items-center justify-center text-6xl">
                              ${nft.attributes.icon}
                            </div>
                          `
                        }
                      }}
                    />
                  )}

                  {/* Rarity Badge */}
                  <div className="absolute top-2 right-2">
                    <div className={`px-2 py-1 rounded-full text-xs font-bold ${
                      nft.rarity === 'common' ? 'bg-gray-500/80 text-white' :
                      nft.rarity === 'rare' ? 'bg-blue-500/80 text-white' :
                      nft.rarity === 'epic' ? 'bg-purple-500/80 text-white' :
                      'bg-yellow-500/80 text-black'
                    }`}>
                      {nft.rarity === 'legendary' && <Sparkles className="w-3 h-3 inline mr-1" />}
                      {rarityLabels[nft.rarity]}
                    </div>
                  </div>
                </div>

                {/* NFT Info */}
                <div className="space-y-2">
                  <div>
                    <h3 className="font-bold text-sm truncate">{nft.name}</h3>
                    <p className="text-xs text-tg-hint">{nft.collection}</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-xs text-tg-hint">#{nft.attributes.luckyNumber}</span>
                    <div className="flex items-center space-x-1">
                      <span className="text-sm font-bold text-blue-400">{nft.value.toFixed(3)} TON</span>
                      <div className="w-1 h-1 bg-green-400 rounded-full animate-pulse" title="Live price" />
                    </div>
                  </div>

                  {/* Fragment Link */}
                  {nft.fragmentUrl && (
                    <div className="text-xs text-blue-400 truncate">
                      Fragment #{nft.id.split('-')[1]}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🎁</div>
          <h3 className="text-lg font-semibold mb-2">No NFTs Found</h3>
          <p className="text-tg-hint">
            {searchQuery || selectedRarity !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Open some chests to start your collection!'
            }
          </p>
        </div>
      )}

      {/* NFT Detail Modal */}
      {selectedNft && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
          onClick={() => setSelectedNft(null)}
        >
          <motion.div
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.5, opacity: 0 }}
            className="bg-tg-bg rounded-3xl p-6 max-w-sm w-full mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="space-y-4">
              {/* NFT Display */}
              <div className="aspect-square bg-gray-100 rounded-2xl overflow-hidden">
                {selectedNft.lottieUrl ? (
                  <LottieAnimation url={selectedNft.lottieUrl} />
                ) : (
                  <img
                    src={selectedNft.image}
                    alt={selectedNft.name}
                    className="w-full h-full object-cover"
                  />
                )}
              </div>

              {/* NFT Details */}
              <div className="space-y-3">
                <div>
                  <h2 className="text-xl font-bold">{selectedNft.name}</h2>
                  <p className="text-tg-hint">{selectedNft.description}</p>
                </div>

                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="backdrop-blur-sm rounded-lg p-3 border border-white/10" style={{backgroundColor: 'rgba(var(--tg-secondary-bg-rgb, 24, 24, 24), 0.5)'}}>
                    <p className="text-tg-hint">Lucky Number</p>
                    <p className="font-bold text-lg">#{selectedNft.attributes.luckyNumber}</p>
                  </div>
                  <div className="backdrop-blur-sm rounded-lg p-3 border border-white/10" style={{backgroundColor: 'rgba(var(--tg-secondary-bg-rgb, 24, 24, 24), 0.5)'}}>
                    <p className="text-tg-hint">Value</p>
                    <p className="font-bold text-lg text-blue-400">{selectedNft.value.toFixed(3)} TON</p>
                  </div>
                  <div className="backdrop-blur-sm rounded-lg p-3 border border-white/10" style={{backgroundColor: 'rgba(var(--tg-secondary-bg-rgb, 24, 24, 24), 0.5)'}}>
                    <p className="text-tg-hint">Collection</p>
                    <p className="font-semibold">{selectedNft.collection}</p>
                  </div>
                  <div className="backdrop-blur-sm rounded-lg p-3 border border-white/10" style={{backgroundColor: 'rgba(var(--tg-secondary-bg-rgb, 24, 24, 24), 0.5)'}}>
                    <p className="text-tg-hint">Rarity</p>
                    <div className="flex items-center space-x-1">
                      {selectedNft.rarity === 'legendary' && <Sparkles className="w-4 h-4 text-yellow-400" />}
                      <p className={`font-bold ${
                        selectedNft.rarity === 'common' ? 'text-gray-400' :
                        selectedNft.rarity === 'rare' ? 'text-blue-400' :
                        selectedNft.rarity === 'epic' ? 'text-purple-400' :
                        'text-yellow-400'
                      }`}>
                        {rarityLabels[selectedNft.rarity]}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button
                    onClick={() => handleShare(selectedNft)}
                    className="flex-1 btn-secondary flex items-center justify-center space-x-2"
                  >
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                  <button
                    onClick={() => handleViewOnMarketplace(selectedNft)}
                    className="flex-1 btn-primary flex items-center justify-center space-x-2"
                  >
                    <ExternalLink className="w-4 h-4" />
                    <span>Fragment</span>
                  </button>
                </div>

                {/* Fragment Link */}
                {selectedNft.fragmentUrl && (
                  <div className="text-center">
                    <p className="text-xs text-tg-hint mb-2">View on Fragment marketplace:</p>
                    <a
                      href={selectedNft.fragmentUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-400 text-sm font-mono break-all hover:underline"
                    >
                      {selectedNft.fragmentUrl}
                    </a>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  )
}

export default NFTGallery
