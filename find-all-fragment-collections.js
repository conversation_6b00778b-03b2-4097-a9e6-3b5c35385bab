// Поиск ВСЕХ Fragment коллекций
console.log('🔍 Finding ALL Fragment collections...')

const TON_API_KEY = '7125266008:c0ad27aa61ea792a52b4e7b39f9b1ed46751'
const TON_API_BASE = 'https://tonapi.io/v2'

async function findAllFragmentCollections() {
  try {
    console.log('🚀 Searching for all Fragment collections...')
    
    const allCollections = []
    let offset = 0
    const limit = 100
    
    // Получаем все популярные коллекции
    while (true) {
      console.log(`🔍 Fetching collections batch ${Math.floor(offset/limit) + 1}...`)
      
      const response = await fetch(`${TON_API_BASE}/nfts/collections?limit=${limit}&offset=${offset}`, {
        headers: {
          'X-API-Key': TON_API_KEY,
          'Accept': 'application/json'
        }
      })

      if (!response.ok) {
        console.log(`❌ Failed to fetch batch: ${response.status}`)
        break
      }

      const data = await response.json()
      
      if (!data.nft_collections || data.nft_collections.length === 0) {
        console.log('✅ No more collections found')
        break
      }
      
      allCollections.push(...data.nft_collections)
      offset += limit
      
      console.log(`📦 Total collections so far: ${allCollections.length}`)
      
      // Ограничиваем поиск первыми 500 коллекциями
      if (offset >= 500) break
    }
    
    console.log(`\n📊 Total collections fetched: ${allCollections.length}`)
    
    // Фильтруем Fragment коллекции
    const fragmentCollections = allCollections.filter(collection => {
      const name = collection.metadata?.name?.toLowerCase() || ''
      const description = collection.metadata?.description?.toLowerCase() || ''
      
      // Ищем ключевые слова Fragment
      const isFragment = 
        name.includes('fragment') ||
        name.includes('telegram') ||
        name.includes('gift') ||
        name.includes('loot') ||
        name.includes('pepe') ||
        name.includes('durov') ||
        name.includes('hex') ||
        name.includes('pot') ||
        name.includes('bag') ||
        name.includes('cap') ||
        name.includes('gem') ||
        name.includes('hat') ||
        name.includes('cake') ||
        name.includes('calendar') ||
        name.includes('eye') ||
        description.includes('telegram') ||
        description.includes('fragment') ||
        description.includes('exclusive nft collection') ||
        description.includes('showcase these unique nfts') ||
        description.includes('gifts section')
      
      return isFragment
    })
    
    console.log(`\n🎁 Found ${fragmentCollections.length} potential Fragment collections:`)
    
    // Проверяем каждую коллекцию
    const verifiedCollections = []
    
    for (let i = 0; i < Math.min(fragmentCollections.length, 20); i++) {
      const collection = fragmentCollections[i]
      console.log(`\n🔍 Checking collection ${i + 1}: ${collection.metadata?.name}`)
      console.log(`  Address: ${collection.address}`)
      console.log(`  Description: ${collection.metadata?.description?.substring(0, 100)}...`)
      
      // Получаем несколько NFT из коллекции для проверки
      const nfts = await getCollectionNFTs(collection.address, 3)
      
      if (nfts.length > 0) {
        console.log(`  ✅ Found ${nfts.length} NFTs:`)
        nfts.forEach((nft, index) => {
          console.log(`    ${index + 1}. ${nft.metadata?.name}`)
          if (nft.metadata?.image) {
            console.log(`       Image: ${nft.metadata.image}`)
          }
        })
        
        // Проверяем, что это действительно Fragment NFT
        const isRealFragment = nfts.some(nft => 
          nft.metadata?.image?.includes('nft.fragment.com') ||
          nft.metadata?.lottie?.includes('nft.fragment.com') ||
          nft.metadata?.name?.includes('#')
        )
        
        if (isRealFragment) {
          console.log(`  🎯 VERIFIED Fragment collection!`)
          verifiedCollections.push({
            address: collection.address,
            name: collection.metadata?.name,
            description: collection.metadata?.description,
            total_items: collection.next_item_index,
            sample_nfts: nfts
          })
        } else {
          console.log(`  ❌ Not a real Fragment collection`)
        }
      } else {
        console.log(`  ❌ No NFTs found`)
      }
      
      // Пауза между запросами
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    
    return verifiedCollections
    
  } catch (error) {
    console.error('💥 Error finding Fragment collections:', error)
    return []
  }
}

async function getCollectionNFTs(collectionAddress, limit = 5) {
  try {
    const response = await fetch(`${TON_API_BASE}/nfts/collections/${collectionAddress}/items?limit=${limit}`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      return []
    }

    const data = await response.json()
    return data.nft_items || []
  } catch (error) {
    return []
  }
}

// Запускаем поиск
findAllFragmentCollections().then(collections => {
  console.log('\n🎉 Fragment collection search completed!')
  console.log(`\n📊 VERIFIED Fragment collections: ${collections.length}`)
  
  if (collections.length > 0) {
    console.log('\n🏆 REAL Fragment collections found:')
    collections.forEach((collection, index) => {
      console.log(`\n${index + 1}. ${collection.name}`)
      console.log(`   Address: ${collection.address}`)
      console.log(`   Items: ${collection.total_items}`)
      console.log(`   Sample NFTs: ${collection.sample_nfts.length}`)
    })
    
    // Генерируем код для интеграции
    console.log('\n💻 Code for integration:')
    console.log('const FRAGMENT_COLLECTIONS = {')
    collections.forEach(collection => {
      const key = collection.name.toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .substring(0, 20)
      console.log(`  '${key}': '${collection.address}',`)
    })
    console.log('}')
    
    // Статистика
    const totalItems = collections.reduce((sum, col) => sum + (col.total_items || 0), 0)
    console.log(`\n📈 Statistics:`)
    console.log(`  Total Collections: ${collections.length}`)
    console.log(`  Total NFTs: ${totalItems}`)
    console.log(`  Average per collection: ${Math.round(totalItems / collections.length)}`)
  } else {
    console.log('\n😞 No verified Fragment collections found')
  }
}).catch(error => {
  console.error('💥 Search failed:', error)
})
