import { WithTrackableState } from '../../classes/WithTrackableState.js';
import { PostEvent } from '../../bridge/methods/postEvent.js';
import { ClosingBehaviorState } from './types.js';

/**
 * @see Usage: https://docs.telegram-mini-apps.com/platform/closing-behavior
 * @see API: https://docs.telegram-mini-apps.com/packages/telegram-apps-sdk/components/closing-behavior
 */
export declare class ClosingBehavior extends WithTrackableState<ClosingBehaviorState> {
    private readonly postEvent;
    constructor(isConfirmationNeeded: boolean, postEvent: PostEvent);
    private set isConfirmationNeeded(value);
    /**
     * True, if the confirmation dialog should be shown while the user is trying to close
     * the Mini App.
     */
    get isConfirmationNeeded(): boolean;
    /**
     * Disables the confirmation dialog when closing the Mini App.
     */
    disableConfirmation(): void;
    /**
     * Enables the confirmation dialog when closing the Mini App.
     */
    enableConfirmation(): void;
}
