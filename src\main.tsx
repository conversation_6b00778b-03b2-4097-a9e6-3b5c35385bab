import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { TonConnectUIProvider } from '@tonconnect/ui-react'

// Telegram Web App initialization
if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
  window.Telegram.WebApp.ready()
  window.Telegram.WebApp.expand()

  // Set theme based on Telegram theme
  const isDark = window.Telegram.WebApp.colorScheme === 'dark'
  document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light')
  document.body.setAttribute('data-theme', isDark ? 'dark' : 'light')

  // Update CSS variables with Telegram theme colors
  if (window.Telegram.WebApp.themeParams) {
    const root = document.documentElement
    const params = window.Telegram.WebApp.themeParams

    root.style.setProperty('--tg-color-bg', params.bg_color || (isDark ? '#212121' : '#ffffff'))
    root.style.setProperty('--tg-color-text', params.text_color || (isDark ? '#ffffff' : '#000000'))
    root.style.setProperty('--tg-color-hint', params.hint_color || (isDark ? '#aaaaaa' : '#999999'))
    root.style.setProperty('--tg-color-link', params.link_color || (isDark ? '#8bb8e8' : '#2481cc'))
    root.style.setProperty('--tg-color-button', params.button_color || (isDark ? '#8bb8e8' : '#2481cc'))
    root.style.setProperty('--tg-color-button-text', params.button_text_color || '#ffffff')
    root.style.setProperty('--tg-color-secondary-bg', params.secondary_bg_color || (isDark ? '#181818' : '#f1f1f1'))
  }
} else {
  // Fallback for development
  document.documentElement.setAttribute('data-theme', 'dark')
  document.body.setAttribute('data-theme', 'dark')
}

const manifestUrl = 'http://localhost:5173/tonconnect-manifest.json'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <TonConnectUIProvider manifestUrl={manifestUrl}>
      <App />
    </TonConnectUIProvider>
  </React.StrictMode>,
)
