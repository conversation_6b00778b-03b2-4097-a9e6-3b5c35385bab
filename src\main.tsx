import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { TonConnectUIProvider } from '@tonconnect/ui-react'

// Telegram Web App initialization
if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
  window.Telegram.WebApp.ready()
  window.Telegram.WebApp.expand()

  // Set theme based on Telegram theme
  const isDark = window.Telegram.WebApp.colorScheme === 'dark'
  document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light')
  document.body.setAttribute('data-theme', isDark ? 'dark' : 'light')

  // Update CSS variables with Telegram theme colors
  if (window.Telegram.WebApp.themeParams) {
    const root = document.documentElement
    const params = window.Telegram.WebApp.themeParams

    const bgColor = params.bg_color || (isDark ? '#212121' : '#ffffff')
    const secondaryBgColor = params.secondary_bg_color || (isDark ? '#181818' : '#f1f1f1')

    root.style.setProperty('--tg-color-bg', bgColor)
    root.style.setProperty('--tg-color-text', params.text_color || (isDark ? '#ffffff' : '#000000'))
    root.style.setProperty('--tg-color-hint', params.hint_color || (isDark ? '#aaaaaa' : '#999999'))
    root.style.setProperty('--tg-color-link', params.link_color || (isDark ? '#8bb8e8' : '#2481cc'))
    root.style.setProperty('--tg-color-button', params.button_color || (isDark ? '#8bb8e8' : '#2481cc'))
    root.style.setProperty('--tg-color-button-text', params.button_text_color || '#ffffff')
    root.style.setProperty('--tg-color-secondary-bg', secondaryBgColor)

    // Convert hex to RGB for opacity support
    const hexToRgb = (hex: string) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` : '24, 24, 24'
    }

    root.style.setProperty('--tg-secondary-bg-rgb', hexToRgb(secondaryBgColor))
  }
} else {
  // Fallback for development
  document.documentElement.setAttribute('data-theme', 'dark')
  document.body.setAttribute('data-theme', 'dark')

  // Set default CSS variables for development
  const root = document.documentElement
  root.style.setProperty('--tg-color-bg', '#212121')
  root.style.setProperty('--tg-color-text', '#ffffff')
  root.style.setProperty('--tg-color-hint', '#aaaaaa')
  root.style.setProperty('--tg-color-link', '#8bb8e8')
  root.style.setProperty('--tg-color-button', '#8bb8e8')
  root.style.setProperty('--tg-color-button-text', '#ffffff')
  root.style.setProperty('--tg-color-secondary-bg', '#181818')
  root.style.setProperty('--tg-secondary-bg-rgb', '24, 24, 24')
}

const manifestUrl = 'http://localhost:5173/tonconnect-manifest.json'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <TonConnectUIProvider manifestUrl={manifestUrl}>
      <App />
    </TonConnectUIProvider>
  </React.StrictMode>,
)
