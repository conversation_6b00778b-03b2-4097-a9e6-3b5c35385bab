import { ThemeParams } from '../components/ThemeParams/ThemeParams.js';
import { MiniApp } from '../components/MiniApp/MiniApp.js';
import { CleanupFn } from '../types/index.js';

export interface GetMiniAppCSSVarNameFn {
    /**
     * @param property - MiniApp property.
     * @returns Computed complete CSS variable name.
     */
    (property: 'bg' | 'header'): string;
}
/**
 * Creates CSS variables connected with the MiniApps class instance background and header colors
 * based on the passed MiniApp and ThemeParams instances.
 *
 * Created variables by default:
 * - `--tg-bg-color`
 * - `--tg-header-color`
 *
 * Variables are being automatically updated in case, corresponding MiniApp and ThemeParams
 * properties were updated.
 *
 * @param miniApp - MiniApp instance.
 * @param themeParams - ThemeParams instance.
 * @param getVarName - function, returning complete CSS variable name for the specified
 * MiniApp property.
 * @returns Function to stop updating variables.
 */
export declare function bindMiniAppCSSVars(miniApp: MiniApp, themeParams: ThemeParams, getVarName?: GetMiniAppCSSVarNameFn): CleanupFn;
