import { WithSupports } from '../../classes/WithSupports.js';
import { PostEvent } from '../../bridge/methods/postEvent.js';
import { ImpactHapticFeedbackStyle, NotificationHapticFeedbackType } from '../../bridge/methods/types/haptic.js';
import { Version } from '../../version/types.js';

/**
 * @see Usage: https://docs.telegram-mini-apps.com/platform/haptic-feedback
 * @see API: https://docs.telegram-mini-apps.com/packages/telegram-apps-sdk/components/haptic-feedback
 */
export declare class HapticFeedback extends WithSupports<'impactOccurred' | 'notificationOccurred' | 'selectionChanged'> {
    private readonly postEvent;
    constructor(version: Version, postEvent: PostEvent);
    /**
     * A method tells that an impact occurred. The Telegram app may play the
     * appropriate haptics based on style value passed.
     * @param style - impact style.
     */
    impactOccurred(style: ImpactHapticFeedbackStyle): void;
    /**
     * A method tells that a task or action has succeeded, failed, or produced
     * a warning. The Telegram app may play the appropriate haptics based on
     * type value passed.
     * @param type - notification type.
     */
    notificationOccurred(type: NotificationHapticFeedbackType): void;
    /**
     * A method tells that the user has changed a selection. The Telegram app
     * may play the appropriate haptics.
     *
     * Do not use this feedback when the user makes or confirms a selection;
     * use it only when the selection changes.
     */
    selectionChanged(): void;
}
