import { MiniAppsMethodVersionedParams, MiniAppsMethodWithVersionedParams } from '../bridge/methods/types/methods.js';
import { SupportsFn } from './types.js';
import { Version } from '../version/types.js';

type HasCheckSupportMethodTuple = {
    [M in MiniAppsMethodWithVersionedParams]: [M, MiniAppsMethodVersionedParams<M>];
}[MiniAppsMethodWithVersionedParams];
/**
 * Returns function, which accepts predefined method name and checks if it is supported
 * via passed schema and version.
 * @param schema - object which contains methods names and TWA methods with specified parameter
 * as a dependency.
 * @param version - platform version.
 */
export declare function createSupportsParamFn<Method extends string>(version: Version, schema: Record<Method, HasCheckSupportMethodTuple>): SupportsFn<Method>;
export {};
