import { Logger } from '../logger/Logger.js';

export declare const logger: Logger;
/**
 * Sets new debug mode. Enabling debug mode leads to printing additional messages in the console,
 * related to the processes inside the package.
 * @param enable - should debug be enabled.
 */
export declare function setDebug(enable: boolean): void;
/**
 * Logs info message into the console.
 * @param args - additional arguments.
 */
export declare function log(...args: any[]): void;
