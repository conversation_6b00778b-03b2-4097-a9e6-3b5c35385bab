import { PostEvent } from '../../bridge/methods/postEvent.js';
import { EventEmitter } from '../../events/event-emitter/EventEmitter.js';
import { BasicNavigatorAnyHistoryItem, BasicNavigatorEvents, BasicNavigatorHistoryItem } from './types.js';

type Emitter<Params> = EventEmitter<BasicNavigatorEvents<Params>>;
export declare class BasicNavigator<Params = {}> {
    /**
     * Currently active history item.
     */
    private _index;
    /**
     * Function to call Mini Apps methods.
     * @default Global `postEvent` function.
     */
    private readonly postEvent;
    /**
     * Navigation history.
     */
    readonly history: Readonly<BasicNavigatorHistoryItem<Params>>[];
    private readonly ee;
    constructor(
    /**
     * Navigation history.
     */
    history: readonly BasicNavigatorAnyHistoryItem<Params>[], 
    /**
     * Currently active history item.
     */
    _index: number, 
    /**
     * Function to call Mini Apps methods.
     * @default Global `postEvent` function.
     */
    postEvent?: PostEvent);
    /**
     * True, if current navigator is currently attached.
     */
    private attached;
    /**
     * Allows this navigator to control the `BackButton` visibility state. It also tracks the
     * `BackButton` clicks and calls the `back` method.
     */
    attach(): void;
    /**
     * Goes to the previous history item.
     */
    back: () => void;
    /**
     * Currently active history item.
     */
    get current(): Readonly<BasicNavigatorHistoryItem<Params>>;
    /**
     * Prevents current navigator from controlling the BackButton visibility state.
     */
    detach(): void;
    /**
     * Goes to the next history item.
     */
    forward(): void;
    /**
     * Changes currently active history item index by the specified delta. This method doesn't
     * change index in case, the updated index points to the non-existing history item. This behavior
     * is preserved until the `fit` argument is specified.
     * @param delta - index delta.
     * @param fit - cuts the delta argument to fit the bounds `[0, history.length - 1]`.
     */
    go(delta: number, fit?: boolean): void;
    /**
     * Goes to the specified index. Method does nothing in case, passed index is out of bounds.
     *
     * If "fit" option was specified and index is out of bounds, it will be cut to the nearest
     * bound.
     * @param index - target index.
     * @param fit - cuts the index argument to fit the bounds `[0, history.length - 1]`.
     */
    goTo(index: number, fit?: boolean): void;
    /**
     * True if navigator has items before the current item.
     */
    get hasPrev(): boolean;
    /**
     * True if navigator has items after the current item.
     */
    get hasNext(): boolean;
    /**
     * Currently active history item index.
     */
    get index(): number;
    /**
     * Adds new event listener.
     */
    on: Emitter<Params>['on'];
    /**
     * Removes event listener.
     */
    off: Emitter<Params>['off'];
    /**
     * Adds a new history item removing all after the current one.
     * @param item - item to add.
     */
    push(item: BasicNavigatorAnyHistoryItem<Params>): void;
    /**
     * Replaces the current history item.
     * @param item - item to replace the current item with.
     */
    replace(item: BasicNavigatorAnyHistoryItem<Params>): void;
    /**
     * Sets history item by the specified index.
     * @param index - history item index to replace.
     * @param historyItem - history item to set.
     */
    private replaceAndMove;
    /**
     * Actualizes the `BackButton` visibility state.
     */
    private sync;
}
export {};
