import { WithStateUtils } from './WithStateUtils.js';
import { MiniAppsMethodName } from '../bridge/methods/types/methods.js';
import { SupportsFn } from '../supports/types.js';
import { Version } from '../version/types.js';

export declare class WithSupportsAndStateUtils<StateShape extends object, SupportsMethod extends string> extends WithStateUtils<StateShape> {
    constructor(
    /**
     * Initial state.
     */
    stateShape: StateShape, 
    /**
     * Mini Apps version.
     */
    version: Version, 
    /**
     * Supports method schema.
     */
    supportsSchema: Record<SupportsMethod, MiniAppsMethodName>);
    /**
     * @returns True, if specified method is supported by the current component.
     */
    supports: SupportsFn<SupportsMethod>;
}
