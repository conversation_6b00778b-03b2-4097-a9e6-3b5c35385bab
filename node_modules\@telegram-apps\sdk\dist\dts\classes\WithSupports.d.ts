import { MiniAppsMethodName } from '../bridge/methods/types/methods.js';
import { SupportsFn } from '../supports/types.js';
import { Version } from '../version/types.js';

export declare class WithSupports<SupportsMethod extends string> {
    constructor(
    /**
     * Mini Apps version.
     */
    version: Version, 
    /**
     * Supports method schema.
     */
    supportsSchema: Record<SupportsMethod, MiniAppsMethodName>);
    /**
     * @returns True, if specified method is supported by the current component.
     */
    supports: SupportsFn<SupportsMethod>;
}
