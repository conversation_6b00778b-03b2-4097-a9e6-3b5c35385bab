/**
 * Creates resettable singleton. We mostly need it for test purposes.
 * @param create - function which creates singleton entity.
 * @param onReset - function which will be called in case, singleton was reset.
 */
export declare function createSingleton<T>(create: (reset: () => void) => T, onReset?: (entity: T) => void): [
    /**
     * Returns singleton entity.
     */
    get: () => T,
    /**
     * Resets last stored entity.
     */
    reset: () => void
];
