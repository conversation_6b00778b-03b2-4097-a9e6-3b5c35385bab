import { Version } from '../../version/types.js';
import { PostEvent } from './postEvent.js';

/**
 * Creates a function which checks if specified method and parameters are supported.
 *
 * If method or parameters are unsupported, an error will be thrown.
 * @param version - Telegram Mini Apps version.
 * @throws {SDKError} ERR_METHOD_UNSUPPORTED
 * @throws {SDKError} ERR_METHOD_PARAMETER_UNSUPPORTED
 * @see ERR_METHOD_UNSUPPORTED
 * @see ERR_METHOD_PARAMETER_UNSUPPORTED
 */
export declare function createPostEvent(version: Version): PostEvent;
