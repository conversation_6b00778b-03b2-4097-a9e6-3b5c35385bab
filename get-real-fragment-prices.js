// Получение РЕАЛЬНЫХ цен с Fragment
console.log('🔍 Getting REAL Fragment prices...')

async function getRealFragmentPrices() {
  try {
    // Пробуем разные методы получения реальных цен
    
    // Метод 1: Прямой запрос к Fragment API
    console.log('🔍 Method 1: Direct Fragment API...')
    const apiResult = await tryFragmentAPI()
    if (apiResult) return apiResult

    // Метод 2: Парсинг конкретных страниц подарков
    console.log('🔍 Method 2: Parse specific gift pages...')
    const giftIds = [
      'lootbag-11217',
      'lootbag-7631', 
      'durovscap-1896',
      'iongem-555'
    ]
    
    const realPrices = []
    for (const giftId of giftIds) {
      const price = await getGiftPrice(giftId)
      if (price) realPrices.push(price)
    }
    
    if (realPrices.length > 0) {
      console.log('✅ Got real prices:', realPrices)
      return realPrices
    }

    // Метод 3: Парсинг через поиск
    console.log('🔍 Method 3: Search Fragment...')
    return await searchFragmentPrices()

  } catch (error) {
    console.error('❌ Failed to get real prices:', error)
    return null
  }
}

async function tryFragmentAPI() {
  const endpoints = [
    'https://fragment.com/api/gifts/search',
    'https://fragment.com/api/marketplace/gifts',
    'https://fragment.com/api/nft/search',
    'https://fragment.com/api/search?type=gifts'
  ]

  for (const endpoint of endpoints) {
    try {
      console.log(`🔍 Trying: ${endpoint}`)
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://fragment.com/gifts'
        },
        body: JSON.stringify({
          query: '',
          type: 'gifts',
          sort: 'price_desc',
          limit: 20
        })
      })

      if (response.ok) {
        const data = await response.json()
        console.log(`✅ API Success:`, data)
        return parseAPIResponse(data)
      }
    } catch (e) {
      console.log(`❌ ${endpoint} failed:`, e.message)
    }
  }
  return null
}

async function getGiftPrice(giftId) {
  try {
    console.log(`🔍 Getting price for: ${giftId}`)
    
    const response = await fetch(`https://fragment.com/gift/${giftId}`, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    const html = await response.text()
    
    // Ищем цену в разных форматах
    const pricePatterns = [
      /(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*TON/gi,
      /price[^>]*>.*?(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*TON/gi,
      /cost[^>]*>.*?(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*TON/gi,
      /"price":\s*"?(\d{1,3}(?:,\d{3})*(?:\.\d+)?)"?/gi,
      /data-price="(\d{1,3}(?:,\d{3})*(?:\.\d+)?)"/gi
    ]
    
    for (const pattern of pricePatterns) {
      const matches = [...html.matchAll(pattern)]
      if (matches.length > 0) {
        const priceStr = matches[0][1].replace(/,/g, '')
        const price = parseFloat(priceStr)
        
        if (price > 0) {
          console.log(`💰 Found price for ${giftId}: ${price} TON`)
          return {
            id: giftId,
            price: price,
            currency: 'TON'
          }
        }
      }
    }
    
    // Ищем в JSON данных
    const jsonMatches = html.match(/window\.__INITIAL_STATE__\s*=\s*({.*?});/s) ||
                       html.match(/window\.__DATA__\s*=\s*({.*?});/s)
    
    if (jsonMatches) {
      try {
        const jsonData = JSON.parse(jsonMatches[1])
        console.log('🔍 Found JSON data:', jsonData)
        
        // Ищем цену в JSON
        const price = findPriceInObject(jsonData)
        if (price) {
          console.log(`💰 Found JSON price for ${giftId}: ${price} TON`)
          return {
            id: giftId,
            price: price,
            currency: 'TON'
          }
        }
      } catch (e) {
        console.log('❌ Failed to parse JSON')
      }
    }
    
    console.log(`❌ No price found for ${giftId}`)
    return null
    
  } catch (error) {
    console.log(`❌ Error getting price for ${giftId}:`, error.message)
    return null
  }
}

function findPriceInObject(obj, path = '') {
  if (typeof obj !== 'object' || obj === null) return null
  
  for (const [key, value] of Object.entries(obj)) {
    const currentPath = path ? `${path}.${key}` : key
    
    // Ищем ключи связанные с ценой
    if (['price', 'cost', 'value', 'amount'].includes(key.toLowerCase())) {
      if (typeof value === 'number' && value > 0) {
        console.log(`🎯 Found price at ${currentPath}: ${value}`)
        return value
      }
      if (typeof value === 'string') {
        const numValue = parseFloat(value.replace(/[^0-9.]/g, ''))
        if (numValue > 0) {
          console.log(`🎯 Found price at ${currentPath}: ${numValue}`)
          return numValue
        }
      }
    }
    
    // Рекурсивный поиск
    if (typeof value === 'object') {
      const found = findPriceInObject(value, currentPath)
      if (found) return found
    }
  }
  
  return null
}

async function searchFragmentPrices() {
  try {
    console.log('🔍 Searching Fragment marketplace...')
    
    const searchUrl = 'https://fragment.com/gifts?sort=price_desc'
    const response = await fetch(searchUrl, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    const html = await response.text()
    
    // Ищем все цены на странице
    const pricePattern = /(\d{1,3}(?:,\d{3})*(?:\.\d+)?)\s*TON/gi
    const prices = [...html.matchAll(pricePattern)]
      .map(match => parseFloat(match[1].replace(/,/g, '')))
      .filter(price => price > 0)
      .sort((a, b) => b - a) // Сортируем по убыванию
    
    console.log(`💰 Found ${prices.length} prices on marketplace:`, prices.slice(0, 10))
    
    return prices.slice(0, 20).map((price, index) => ({
      id: `gift-${index}`,
      price: price,
      currency: 'TON'
    }))
    
  } catch (error) {
    console.log('❌ Search failed:', error.message)
    return null
  }
}

function parseAPIResponse(data) {
  // Парсим ответ API
  if (data.gifts && Array.isArray(data.gifts)) {
    return data.gifts.map(gift => ({
      id: gift.id,
      name: gift.name,
      price: gift.price || gift.current_price || 0,
      currency: gift.currency || 'TON'
    }))
  }
  
  if (Array.isArray(data)) {
    return data.map(item => ({
      id: item.id,
      price: item.price || 0,
      currency: 'TON'
    }))
  }
  
  return null
}

// Запускаем получение реальных цен
getRealFragmentPrices().then(prices => {
  if (prices && prices.length > 0) {
    console.log('\n🎉 SUCCESS! Got real Fragment prices:')
    prices.forEach((item, index) => {
      console.log(`${index + 1}. ${item.id}: ${item.price} ${item.currency}`)
    })
    
    // Выводим статистику
    const priceValues = prices.map(p => p.price).filter(p => p > 0)
    if (priceValues.length > 0) {
      console.log('\n📊 Price Statistics:')
      console.log(`💰 Highest: ${Math.max(...priceValues)} TON`)
      console.log(`💰 Lowest: ${Math.min(...priceValues)} TON`)
      console.log(`💰 Average: ${(priceValues.reduce((a, b) => a + b, 0) / priceValues.length).toFixed(2)} TON`)
    }
  } else {
    console.log('\n😞 Failed to get real Fragment prices')
  }
}).catch(error => {
  console.error('💥 Error:', error)
})
