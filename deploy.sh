#!/bin/bash

# Telegram NFT Gifts Mini App - Deploy Script
# Автоматический деплой на Vercel и Railway

echo "🚀 Starting deployment of Telegram NFT Gifts Mini App..."

# Проверка зависимостей
command -v vercel >/dev/null 2>&1 || { echo "❌ Vercel CLI not installed. Run: npm i -g vercel"; exit 1; }
command -v railway >/dev/null 2>&1 || { echo "❌ Railway CLI not installed. Run: npm i -g @railway/cli"; exit 1; }

# Проверка переменных окружения
if [ -z "$BOT_TOKEN" ]; then
    echo "❌ BOT_TOKEN environment variable is required"
    exit 1
fi

echo "📦 Building frontend..."
npm run build

echo "🌐 Deploying frontend to Vercel..."
vercel --prod

# Получаем URL от Vercel
WEBAPP_URL=$(vercel ls --scope=personal | grep -E "https://.*\.vercel\.app" | head -1 | awk '{print $2}')

if [ -z "$WEBAPP_URL" ]; then
    echo "❌ Failed to get Vercel URL"
    exit 1
fi

echo "✅ Frontend deployed to: $WEBAPP_URL"

echo "🤖 Deploying bot to Railway..."
cd bot

# Создаем временный .env для Railway
cat > .env.production << EOF
BOT_TOKEN=$BOT_TOKEN
WEBAPP_URL=$WEBAPP_URL
PORT=3000
EOF

# Деплой на Railway
railway up

echo "✅ Bot deployed to Railway"

echo "🎉 Deployment completed!"
echo "📱 Update your bot's Web App URL in @BotFather to: $WEBAPP_URL"
echo "🔧 Don't forget to update the TON Connect manifest URL"

# Очистка
rm -f .env.production

echo "🎮 Your NFT Gifts Mini App is ready!"
