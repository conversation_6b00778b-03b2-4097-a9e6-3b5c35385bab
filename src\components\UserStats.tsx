import { Star, Coins, Ticket } from 'lucide-react'
import { UserBalance } from '../types/nft'

interface UserStatsProps {
  balance: UserBalance
}

const UserStats = ({ balance }: UserStatsProps) => {
  return (
    <div className="px-4 py-3">
      <div className="grid grid-cols-3 gap-3">
        {/* Stars */}
        <div className="card flex items-center space-x-3 hover:scale-105 transition-transform">
          <div className="relative w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg">
            <Star className="w-5 h-5 text-white fill-current" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-white/30 rounded-full animate-ping" />
          </div>
          <div className="flex-1">
            <p className="text-xs text-tg-hint font-medium">Stars</p>
            <p className="text-lg font-bold text-yellow-400">{balance.stars.toLocaleString()}</p>
          </div>
        </div>

        {/* TON */}
        <div className="card flex items-center space-x-3 hover:scale-105 transition-transform">
          <div className="relative w-10 h-10 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
            <Coins className="w-5 h-5 text-white" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-white/30 rounded-full animate-ping" />
          </div>
          <div className="flex-1">
            <p className="text-xs text-tg-hint font-medium">TON</p>
            <p className="text-lg font-bold text-blue-400">{balance.ton.toFixed(3)}</p>
          </div>
        </div>

        {/* Tickets */}
        <div className="card flex items-center space-x-3 hover:scale-105 transition-transform">
          <div className="relative w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
            <Ticket className="w-5 h-5 text-white" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-white/30 rounded-full animate-ping" />
          </div>
          <div className="flex-1">
            <p className="text-xs text-tg-hint font-medium">Tickets</p>
            <p className="text-lg font-bold text-purple-400">{balance.tickets}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserStats
