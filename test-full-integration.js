// Полный тест интеграции Fragment NFT
console.log('🔍 Testing full Fragment NFT integration...')

const TON_API_KEY = '7125266008:c0ad27aa61ea792a52b4e7b39f9b1ed46751'
const TON_API_BASE = 'https://tonapi.io/v2'

// Известные Fragment коллекции
const FRAGMENT_COLLECTIONS = {
  'hexpots': '0:7a02d04f3ae4ed42697ca1203b5fc66a27942670e618aa8cfddac4185c6510af',
  'telegramusernames': '0:44225c78855e9455e6cad879863831f86b415e2e83c967480c4f5f6a3788e4ea',
}

async function testFullIntegration() {
  try {
    console.log('🚀 Starting full integration test...')
    
    // Тест 1: Получить все Fragment коллекции
    console.log('\n📦 Test 1: Get all Fragment collections...')
    const collections = await getAllFragmentCollections()
    
    if (collections.length > 0) {
      console.log(`✅ Found ${collections.length} Fragment collections:`)
      collections.forEach((collection, index) => {
        console.log(`  ${index + 1}. ${collection.name} (${collection.total_items} items)`)
        console.log(`     Address: ${collection.address}`)
        console.log(`     Floor: ${collection.floor_price} TON, Top: ${collection.top_price} TON`)
      })
    } else {
      console.log('❌ No Fragment collections found')
    }
    
    // Тест 2: Получить случайные NFT из каждой коллекции
    console.log('\n🎁 Test 2: Get random NFTs from collections...')
    const randomNFTs = []
    
    for (const collection of collections) {
      try {
        const nft = await getRandomNFTFromCollection(collection.address)
        if (nft) {
          randomNFTs.push(nft)
          console.log(`✅ ${collection.name}: ${nft.name} - ${nft.price} TON`)
        }
      } catch (error) {
        console.log(`❌ Failed to get NFT from ${collection.name}:`, error.message)
      }
    }
    
    // Тест 3: Симуляция открытия сундука
    console.log('\n🎲 Test 3: Simulate chest opening...')
    for (let i = 0; i < 5; i++) {
      const chestReward = await simulateChestOpening()
      if (chestReward) {
        console.log(`  Chest ${i + 1}: ${chestReward.name} - ${chestReward.price} TON (${chestReward.rarity})`)
      }
    }
    
    // Тест 4: Проверить качество данных
    console.log('\n🔍 Test 4: Data quality check...')
    const qualityReport = analyzeDataQuality(randomNFTs)
    console.log('Quality Report:')
    console.log(`  Real Fragment images: ${qualityReport.realImages}/${qualityReport.total}`)
    console.log(`  Has Lottie animations: ${qualityReport.hasLottie}/${qualityReport.total}`)
    console.log(`  Has prices: ${qualityReport.hasPrices}/${qualityReport.total}`)
    console.log(`  Average price: ${qualityReport.avgPrice.toFixed(3)} TON`)
    
    return {
      collections,
      randomNFTs,
      qualityReport
    }
    
  } catch (error) {
    console.error('💥 Full integration test failed:', error)
    return null
  }
}

async function getAllFragmentCollections() {
  const collections = []
  
  // Получаем известные коллекции
  for (const [name, address] of Object.entries(FRAGMENT_COLLECTIONS)) {
    try {
      const collection = await getCollectionInfo(address)
      if (collection) {
        const nfts = await getCollectionNFTs(address, 10)
        
        // Вычисляем статистику цен
        const prices = nfts
          .filter(nft => nft.sale?.price)
          .map(nft => parseFloat(nft.sale.price) / 1000000000)
        
        collections.push({
          address: address,
          name: collection.metadata?.name || name,
          description: collection.metadata?.description || '',
          total_items: collection.next_item_index || nfts.length,
          floor_price: prices.length > 0 ? Math.min(...prices) : 0,
          top_price: prices.length > 0 ? Math.max(...prices) : 0,
          nfts: nfts
        })
      }
    } catch (error) {
      console.log(`❌ Error getting collection ${name}:`, error.message)
    }
  }
  
  // Ищем дополнительные коллекции
  const additionalCollections = await searchAdditionalCollections()
  collections.push(...additionalCollections)
  
  return collections
}

async function searchAdditionalCollections() {
  try {
    console.log('🔍 Searching for additional Fragment collections...')
    
    const fragmentCollections = []
    
    // Получаем популярные коллекции
    const response = await fetch(`${TON_API_BASE}/nfts/collections?limit=50`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) return []

    const data = await response.json()
    
    // Проверяем каждую коллекцию
    for (const collection of data.nft_collections?.slice(0, 20) || []) {
      try {
        const nfts = await getCollectionNFTs(collection.address, 3)
        
        // Проверяем, есть ли Fragment NFT
        const hasFragmentNFTs = nfts.some(nft => {
          const image = nft.metadata?.image || ''
          const lottie = nft.metadata?.lottie || ''
          
          return image.includes('nft.fragment.com') || 
                 lottie.includes('nft.fragment.com')
        })
        
        if (hasFragmentNFTs) {
          console.log(`  ✅ Found additional Fragment collection: ${collection.metadata?.name}`)
          
          const prices = nfts
            .filter(nft => nft.sale?.price)
            .map(nft => parseFloat(nft.sale.price) / 1000000000)
          
          fragmentCollections.push({
            address: collection.address,
            name: collection.metadata?.name || 'Fragment Collection',
            description: collection.metadata?.description || '',
            total_items: collection.next_item_index || nfts.length,
            floor_price: prices.length > 0 ? Math.min(...prices) : 0,
            top_price: prices.length > 0 ? Math.max(...prices) : 0,
            nfts: nfts
          })
        }
        
        await new Promise(resolve => setTimeout(resolve, 200))
        
      } catch (error) {
        // Игнорируем ошибки отдельных коллекций
      }
    }
    
    return fragmentCollections
    
  } catch (error) {
    console.error('Failed to search additional collections:', error)
    return []
  }
}

async function getCollectionInfo(address) {
  try {
    const response = await fetch(`${TON_API_BASE}/nfts/collections/${address}`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) return null
    return await response.json()
  } catch (error) {
    return null
  }
}

async function getCollectionNFTs(collectionAddress, limit = 10) {
  try {
    const response = await fetch(`${TON_API_BASE}/nfts/collections/${collectionAddress}/items?limit=${limit}`, {
      headers: {
        'X-API-Key': TON_API_KEY,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) return []
    const data = await response.json()
    return data.nft_items || []
  } catch (error) {
    return []
  }
}

async function getRandomNFTFromCollection(collectionAddress) {
  try {
    const nfts = await getCollectionNFTs(collectionAddress, 20)
    
    if (nfts.length === 0) return null
    
    const randomNFT = nfts[Math.floor(Math.random() * nfts.length)]
    const price = randomNFT.sale?.price ? parseFloat(randomNFT.sale.price) / 1000000000 : 0
    
    return {
      address: randomNFT.address,
      name: randomNFT.metadata?.name || 'Unknown NFT',
      image: randomNFT.metadata?.image,
      lottie: randomNFT.metadata?.lottie,
      price: price,
      rarity: determineRarity(randomNFT.metadata?.name || ''),
      collection: randomNFT.collection?.name || 'Unknown Collection'
    }
  } catch (error) {
    return null
  }
}

async function simulateChestOpening() {
  // Получаем случайный NFT из любой доступной коллекции
  const collections = Object.values(FRAGMENT_COLLECTIONS)
  const randomCollection = collections[Math.floor(Math.random() * collections.length)]
  
  return await getRandomNFTFromCollection(randomCollection)
}

function determineRarity(name) {
  const number = extractNumberFromName(name)
  if (number <= 100) return 'legendary'
  if (number <= 1000) return 'epic'
  if (number <= 5000) return 'rare'
  return 'common'
}

function extractNumberFromName(name) {
  const match = name.match(/#(\d+)/)
  return match ? parseInt(match[1]) : Math.floor(Math.random() * 50000) + 1
}

function analyzeDataQuality(nfts) {
  const total = nfts.length
  const realImages = nfts.filter(nft => nft.image?.includes('nft.fragment.com')).length
  const hasLottie = nfts.filter(nft => nft.lottie?.includes('nft.fragment.com')).length
  const hasPrices = nfts.filter(nft => nft.price > 0).length
  const avgPrice = nfts.reduce((sum, nft) => sum + nft.price, 0) / total
  
  return {
    total,
    realImages,
    hasLottie,
    hasPrices,
    avgPrice
  }
}

// Запускаем полный тест
testFullIntegration().then(results => {
  if (results) {
    console.log('\n🎉 Full integration test completed successfully!')
    console.log('\n📊 Final Summary:')
    console.log(`✅ Fragment Collections: ${results.collections.length}`)
    console.log(`✅ Random NFTs: ${results.randomNFTs.length}`)
    console.log(`✅ Data Quality Score: ${Math.round((results.qualityReport.realImages / results.qualityReport.total) * 100)}%`)
    
    if (results.collections.length > 0 && results.randomNFTs.length > 0) {
      console.log('\n🚀 Integration is working! App should show real Fragment NFTs!')
    } else {
      console.log('\n⚠️ Integration has issues - check the logs above')
    }
  } else {
    console.log('\n😞 Integration test failed')
  }
}).catch(error => {
  console.error('💥 Test error:', error)
})
