import { State } from './State/State.js';

export declare class WithStateUtils<Shape extends object> {
    protected state: State<Shape>;
    constructor(shape: Shape);
    /**
     * Gets the state value.
     */
    protected get: State<Shape>['get'];
    /**
     * Sets the state value.
     */
    protected set: State<Shape>['set'];
    /**
     * Clones the current state.
     */
    protected clone: State<Shape>['clone'];
}
