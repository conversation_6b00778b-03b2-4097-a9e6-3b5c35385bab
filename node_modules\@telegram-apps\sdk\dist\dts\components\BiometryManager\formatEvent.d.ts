import { BiometryType, MiniAppsEventPayload } from '../../bridge/events/types.js';

export interface FormatBiometryInfoResult {
    /**
     * Shows whether biometry is available.
     */
    available: boolean;
    /**
     * Shows whether permission to use biometrics has been requested.
     */
    accessRequested: boolean;
    /**
     * Shows whether permission to use biometrics has been granted.
     */
    accessGranted: boolean;
    /**
     * A unique device identifier that can be used to match the token to the device.
     */
    deviceId: string;
    /**
     * Show whether local storage contains previously saved token.
     */
    tokenSaved: boolean;
    /**
     * The type of biometrics currently available on the device.
     */
    type: BiometryType;
}
/**
 * Converts `biometry_info_received` to some common shape.
 * @param event - event payload.
 * @see biometry_info_received
 */
export declare function formatEvent(event: MiniAppsEventPayload<'biometry_info_received'>): FormatBiometryInfoResult;
