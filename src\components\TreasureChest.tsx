import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Star, Coins, Gift, Sparkles, Gem, Crown, Zap, Loader2 } from 'lucide-react'
import { ChestType, UserBalance, NFTGift } from '../types/nft'
import { useTelegramWebApp } from '../hooks/useTelegramWebApp'
import { FragmentAPI } from '../services/fragmentApi'
import ChestAnimation from './ChestAnimation'
import RewardModal from './RewardModal'

interface TreasureChestProps {
  balance: UserBalance
  onBalanceUpdate: (balance: UserBalance) => void
}

const TreasureChest = ({ balance, onBalanceUpdate }: TreasureChestProps) => {
  const [selectedChest, setSelectedChest] = useState<ChestType | null>(null)
  const [isOpening, setIsOpening] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [reward, setReward] = useState<NFTGift | null>(null)
  const { hapticFeedback, showAlert } = useTelegramWebApp()

  const chestTypes: ChestType[] = [
    {
      id: 'demo',
      name: 'Demo Chest',
      description: 'Free practice chest with higher chances',
      price: {},
      image: '🎁',
      isDemo: true,
      rewards: [
        { rarity: 'common', chance: 50, items: ['witchhat', 'homemadecake', 'spyagaric'] },
        { rarity: 'rare', chance: 35, items: ['evileye', 'hexpot', 'crystalball'] },
        { rarity: 'epic', chance: 12, items: ['durovscap', 'iongem', 'magicwand'] },
        { rarity: 'legendary', chance: 3, items: ['plushpepe', 'lootbag', 'goldenkey'] }
      ]
    },
    {
      id: 'bronze',
      name: 'Bronze Chest',
      description: 'Basic chest with Fragment gifts',
      price: { stars: 100 },
      image: '📦',
      rewards: [
        { rarity: 'common', chance: 65, items: ['witchhat', 'spyagaric', 'homemadecake', 'deskcalendar'] },
        { rarity: 'rare', chance: 25, items: ['evileye', 'hexpot', 'crystalball', 'magicwand'] },
        { rarity: 'epic', chance: 8, items: ['durovscap', 'iongem', 'goldenkey'] },
        { rarity: 'legendary', chance: 2, items: ['plushpepe', 'lootbag', 'dragonscale'] }
      ]
    },
    {
      id: 'silver',
      name: 'Silver Chest',
      description: 'Premium chest with rare Fragment NFTs',
      price: { stars: 300 },
      image: '🎀',
      rewards: [
        { rarity: 'common', chance: 40, items: ['evileye', 'hexpot', 'crystalball'] },
        { rarity: 'rare', chance: 35, items: ['durovscap', 'iongem', 'magicwand', 'goldenkey'] },
        { rarity: 'epic', chance: 20, items: ['plushpepe', 'lootbag', 'dragonscale', 'phoenixfeather'] },
        { rarity: 'legendary', chance: 5, items: ['unicornhorn', 'starfragment', 'timecapsule'] }
      ]
    },
    {
      id: 'gold',
      name: 'Gold Chest',
      description: 'Luxury chest with legendary Fragment NFTs',
      price: { ton: 0.5 },
      image: '💎',
      rewards: [
        { rarity: 'common', chance: 20, items: ['magicwand', 'goldenkey'] },
        { rarity: 'rare', chance: 35, items: ['dragonscale', 'phoenixfeather'] },
        { rarity: 'epic', chance: 35, items: ['unicornhorn', 'starfragment'] },
        { rarity: 'legendary', chance: 10, items: ['timecapsule', 'plushpepe', 'lootbag'] }
      ]
    }
  ]

  const canAfford = (chest: ChestType): boolean => {
    if (chest.isDemo) return true
    if (chest.price.stars && balance.stars < chest.price.stars) return false
    if (chest.price.ton && balance.ton < chest.price.ton) return false
    return true
  }

  const openChest = async (chest: ChestType) => {
    if (!canAfford(chest)) {
      showAlert('Insufficient balance!')
      hapticFeedback.notification('error')
      return
    }

    setSelectedChest(chest)
    setIsLoading(true)
    hapticFeedback.impact('medium')

    try {
      // Get real reward from Fragment API
      const reward = await generateReward(chest)
      setReward(reward)
      setIsOpening(true)
      setIsLoading(false)

      // Update balance
      if (!chest.isDemo) {
        const newBalance = { ...balance }
        if (chest.price.stars) newBalance.stars -= chest.price.stars
        if (chest.price.ton) newBalance.ton -= chest.price.ton
        onBalanceUpdate(newBalance)
      }

      hapticFeedback.notification('success')
    } catch (error) {
      console.error('Failed to open chest:', error)
      showAlert('Failed to open chest. Please try again.')
      setIsLoading(false)
      setSelectedChest(null)
      hapticFeedback.notification('error')
    }
  }

  const generateReward = async (chest: ChestType): Promise<NFTGift> => {
    console.log(`🎁 Opening ${chest.name}...`)

    const random = Math.random() * 100
    let cumulativeChance = 0

    for (const rewardTier of chest.rewards) {
      cumulativeChance += rewardTier.chance
      if (random <= cumulativeChance) {
        console.log(`🎯 Selected ${rewardTier.rarity} tier (${rewardTier.chance}% chance)`)

        // Получаем случайный подарок из любой доступной коллекции
        // Не используем старые ID, а получаем реальный Fragment NFT
        return await FragmentAPI.getRandomGift()
      }
    }

    // Fallback - получаем любой случайный Fragment NFT
    console.log('🔄 Using fallback - getting random Fragment NFT')
    return await FragmentAPI.getRandomGift()
  }

  const closeModal = () => {
    setSelectedChest(null)
    setReward(null)
    setIsOpening(false)
    setIsLoading(false)
  }

  return (
    <div className="space-y-6">
      {/* Chest Grid */}
      <div className="grid grid-cols-2 gap-4">
        {chestTypes.map((chest) => {
          const affordable = canAfford(chest)
          const ChestIcon = chest.id === 'demo' ? Gift :
                           chest.id === 'bronze' ? Sparkles :
                           chest.id === 'silver' ? Gem : Crown

          return (
            <motion.div
              key={chest.id}
              whileHover={{ scale: affordable && !isLoading ? 1.02 : 1 }}
              whileTap={{ scale: affordable && !isLoading ? 0.98 : 1 }}
              className={`${chest.id === 'gold' ? 'card-premium' : 'card'} cursor-pointer transition-all duration-200 ${
                affordable && !isLoading ? 'hover:shadow-lg' : 'opacity-50'
              } ${chest.isDemo ? 'border-2 border-green-400/50 bg-green-500/10' : ''} ${
                isLoading ? 'pointer-events-none' : ''
              }`}
              onClick={() => !isLoading && openChest(chest)}
            >
              <div className="text-center space-y-3">
                {/* Modern Icon */}
                <div className="relative mx-auto w-16 h-16 flex items-center justify-center">
                  <div className={`absolute inset-0 rounded-2xl ${
                    chest.id === 'demo' ? 'bg-gradient-to-br from-green-400 to-emerald-500' :
                    chest.id === 'bronze' ? 'bg-gradient-to-br from-orange-400 to-amber-500' :
                    chest.id === 'silver' ? 'bg-gradient-to-br from-gray-300 to-slate-400' :
                    'bg-gradient-to-br from-yellow-400 to-orange-500'
                  } shadow-lg`} />
                  {isLoading && selectedChest?.id === chest.id ? (
                    <Loader2 className="relative w-8 h-8 text-white animate-spin" />
                  ) : (
                    <ChestIcon className="relative w-8 h-8 text-white" />
                  )}
                  {chest.id === 'gold' && !isLoading && (
                    <div className="absolute -top-1 -right-1">
                      <Zap className="w-4 h-4 text-yellow-300 animate-pulse" />
                    </div>
                  )}
                </div>

                <div>
                  <h3 className="font-bold text-base">{chest.name}</h3>
                  <p className="text-xs text-tg-hint mt-1">{chest.description}</p>
                </div>

                {/* Price */}
                <div className="flex items-center justify-center space-x-2">
                  {chest.isDemo ? (
                    <div className="px-3 py-1 bg-green-500/20 rounded-full">
                      <span className="text-green-400 font-bold text-sm">FREE</span>
                    </div>
                  ) : (
                    <>
                      {chest.price.stars && (
                        <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 rounded-full">
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                          <span className="text-sm font-bold text-yellow-400">{chest.price.stars}</span>
                        </div>
                      )}
                      {chest.price.ton && (
                        <div className="flex items-center space-x-1 px-2 py-1 bg-blue-500/20 rounded-full">
                          <Coins className="w-4 h-4 text-blue-400" />
                          <span className="text-sm font-bold text-blue-400">{chest.price.ton}</span>
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* Rarity Indicators */}
                <div className="flex justify-center space-x-1">
                  {chest.rewards.map((reward, index) => (
                    <div
                      key={index}
                      className={`w-3 h-1 rounded-full ${
                        reward.rarity === 'common' ? 'bg-gray-400' :
                        reward.rarity === 'rare' ? 'bg-blue-400' :
                        reward.rarity === 'epic' ? 'bg-purple-400' :
                        'bg-yellow-400'
                      } ${reward.rarity === 'legendary' ? 'animate-pulse' : ''}`}
                      title={`${reward.chance}% ${reward.rarity}`}
                    />
                  ))}
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Opening Animation Modal */}
      <AnimatePresence>
        {isOpening && selectedChest && reward && (
          <ChestAnimation
            chest={selectedChest}
            reward={reward}
            onComplete={() => setIsOpening(false)}
          />
        )}
      </AnimatePresence>

      {/* Reward Modal */}
      <AnimatePresence>
        {reward && (
          <RewardModal
            reward={reward}
            onClose={closeModal}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

export default TreasureChest
