import { motion, AnimatePresence } from 'framer-motion'
import { useState, useEffect } from 'react'
import { ChestType, NFTGift } from '../types/nft'
import { <PERSON><PERSON><PERSON>, <PERSON>ap, Crown, Star } from 'lucide-react'

interface ChestAnimationProps {
  chest: ChestType
  reward?: NFTGift
  onComplete: () => void
}

const ChestAnimation = ({ chest, reward, onComplete }: ChestAnimationProps) => {
  const [phase, setPhase] = useState<'opening' | 'revealing' | 'celebrating'>('opening')
  const [showReward, setShowReward] = useState(false)

  useEffect(() => {
    const timer1 = setTimeout(() => setPhase('revealing'), 2000)
    const timer2 = setTimeout(() => {
      setShowReward(true)
      setPhase('celebrating')
    }, 3500)
    const timer3 = setTimeout(() => onComplete(), 6000)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
      clearTimeout(timer3)
    }
  }, [onComplete])

  const getRarityColor = (rarity?: string) => {
    switch (rarity) {
      case 'legendary': return '#F59E0B'
      case 'epic': return '#8B5CF6'
      case 'rare': return '#3B82F6'
      default: return '#9CA3AF'
    }
  }

  const getRarityGlow = (rarity?: string) => {
    switch (rarity) {
      case 'legendary': return 'drop-shadow-[0_0_20px_rgba(245,158,11,0.8)]'
      case 'epic': return 'drop-shadow-[0_0_20px_rgba(139,92,246,0.8)]'
      case 'rare': return 'drop-shadow-[0_0_20px_rgba(59,130,246,0.8)]'
      default: return 'drop-shadow-[0_0_10px_rgba(156,163,175,0.5)]'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center overflow-hidden"
    >
      {/* Background Particles */}
      <div className="absolute inset-0">
        {[...Array(50)].map((_, i) => (
          <motion.div
            key={i}
            initial={{
              opacity: 0,
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
              scale: 0
            }}
            animate={{
              opacity: [0, 1, 0],
              scale: [0, Math.random() * 0.5 + 0.5, 0],
              y: [null, Math.random() * window.innerHeight]
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2
            }}
            className="absolute w-1 h-1 bg-white rounded-full"
          />
        ))}
      </div>

      <div className="text-center space-y-8 relative z-10">
        {/* Phase 1: Opening Animation */}
        <AnimatePresence>
          {phase === 'opening' && (
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 1.5, opacity: 0 }}
              className="space-y-6"
            >
              {/* Chest with Glow Effect */}
              <div className="relative">
                <motion.div
                  animate={{
                    rotateY: [0, 360],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    rotateY: { duration: 2, ease: "easeInOut" },
                    scale: { duration: 1, repeat: Infinity, repeatType: "reverse" }
                  }}
                  className="text-8xl relative z-10"
                  style={{
                    filter: `drop-shadow(0 0 30px ${chest.id === 'gold' ? '#F59E0B' : chest.id === 'silver' ? '#8B5CF6' : '#3B82F6'})`
                  }}
                >
                  {chest.image}
                </motion.div>

                {/* Rotating Glow Ring */}
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="absolute inset-0 flex items-center justify-center"
                >
                  <div className={`w-32 h-32 rounded-full border-4 border-dashed ${
                    chest.id === 'gold' ? 'border-yellow-400' :
                    chest.id === 'silver' ? 'border-purple-400' :
                    'border-blue-400'
                  } opacity-50`} />
                </motion.div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="space-y-2"
              >
                <h2 className="text-3xl font-bold text-white">Opening {chest.name}...</h2>
                <p className="text-white/70">Preparing your reward...</p>
              </motion.div>

              {/* Progress Bar */}
              <div className="w-80 h-3 bg-white/20 rounded-full overflow-hidden mx-auto">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 2, ease: "easeInOut" }}
                  className={`h-full bg-gradient-to-r ${
                    chest.id === 'gold' ? 'from-yellow-400 to-orange-500' :
                    chest.id === 'silver' ? 'from-purple-400 to-pink-500' :
                    'from-blue-400 to-cyan-500'
                  } rounded-full`}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Phase 2: Revealing */}
        <AnimatePresence>
          {phase === 'revealing' && (
            <motion.div
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0, opacity: 0 }}
              className="space-y-6"
            >
              {/* Explosion Effect */}
              <div className="relative">
                {[...Array(12)].map((_, i) => (
                  <motion.div
                    key={i}
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 1, 0],
                      x: Math.cos(i * 30 * Math.PI / 180) * 150,
                      y: Math.sin(i * 30 * Math.PI / 180) * 150,
                    }}
                    transition={{
                      duration: 1.5,
                      ease: "easeOut"
                    }}
                    className="absolute text-4xl"
                    style={{
                      left: '50%',
                      top: '50%',
                      transform: 'translate(-50%, -50%)'
                    }}
                  >
                    {i % 4 === 0 ? '✨' : i % 4 === 1 ? '💫' : i % 4 === 2 ? '⭐' : '🌟'}
                  </motion.div>
                ))}

                {/* Central Flash */}
                <motion.div
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{
                    scale: [0, 3, 0],
                    opacity: [0, 1, 0]
                  }}
                  transition={{ duration: 1 }}
                  className="w-20 h-20 bg-white rounded-full mx-auto"
                />
              </div>

              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-4xl font-bold text-white"
              >
                🎉 REWARD REVEALED! 🎉
              </motion.h2>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Phase 3: Celebrating with Reward */}
        <AnimatePresence>
          {phase === 'celebrating' && showReward && reward && (
            <motion.div
              initial={{ scale: 0, opacity: 0, y: 50 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              transition={{ type: "spring", damping: 15, stiffness: 300 }}
              className="space-y-6"
            >
              {/* Confetti Rain */}
              {[...Array(30)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{
                    y: -100,
                    x: Math.random() * 400 - 200,
                    rotate: 0,
                    opacity: 1
                  }}
                  animate={{
                    y: window.innerHeight + 100,
                    rotate: 360,
                    opacity: 0
                  }}
                  transition={{
                    duration: Math.random() * 2 + 2,
                    delay: Math.random() * 1,
                    ease: "linear"
                  }}
                  className="absolute text-2xl pointer-events-none"
                  style={{
                    left: '50%',
                    color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'][Math.floor(Math.random() * 5)]
                  }}
                >
                  {['🎉', '🎊', '✨', '🌟', '💫'][Math.floor(Math.random() * 5)]}
                </motion.div>
              ))}

              {/* Reward Display */}
              <div className="relative">
                {/* Rarity Glow */}
                <motion.div
                  animate={{
                    scale: [1, 1.1, 1],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className={`absolute inset-0 rounded-3xl blur-xl ${getRarityGlow(reward.rarity)}`}
                  style={{
                    background: `radial-gradient(circle, ${getRarityColor(reward.rarity)}40 0%, transparent 70%)`
                  }}
                />

                {/* NFT Card */}
                <motion.div
                  animate={{
                    rotateY: [0, 10, -10, 0],
                    y: [0, -10, 0]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-3xl p-6 border-2 shadow-2xl"
                  style={{ borderColor: getRarityColor(reward.rarity) }}
                >
                  {/* Rarity Badge */}
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div
                      className="px-4 py-1 rounded-full text-white font-bold text-sm flex items-center space-x-1"
                      style={{ backgroundColor: getRarityColor(reward.rarity) }}
                    >
                      {reward.rarity === 'legendary' && <Crown className="w-4 h-4" />}
                      {reward.rarity === 'epic' && <Zap className="w-4 h-4" />}
                      {reward.rarity === 'rare' && <Sparkles className="w-4 h-4" />}
                      {reward.rarity === 'common' && <Star className="w-4 h-4" />}
                      <span className="uppercase">{reward.rarity}</span>
                    </div>
                  </div>

                  {/* NFT Image */}
                  <div className="w-48 h-48 mx-auto mb-4 rounded-2xl overflow-hidden bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
                    <span className="text-6xl">{reward.attributes.icon}</span>
                  </div>

                  {/* NFT Info */}
                  <div className="text-center space-y-2">
                    <h3 className="text-2xl font-bold text-white">{reward.name}</h3>
                    <p className="text-gray-300">{reward.collection}</p>
                    <div className="flex items-center justify-center space-x-4 text-sm">
                      <span className="text-gray-400">#{reward.attributes.luckyNumber}</span>
                      <span className="text-blue-400 font-bold">{reward.value.toFixed(3)} TON</span>
                    </div>
                  </div>
                </motion.div>
              </div>

              {/* Success Message */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="space-y-2"
              >
                <h2 className="text-3xl font-bold text-white">🎊 Congratulations! 🎊</h2>
                <p className="text-white/80">You received a {reward.rarity} NFT gift!</p>
                <p className="text-sm text-white/60">Tap anywhere to continue</p>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Click to close */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: phase === 'celebrating' ? 1 : 0 }}
        className="absolute inset-0 cursor-pointer"
        onClick={onComplete}
      />
    </motion.div>
  )
}

export default ChestAnimation
