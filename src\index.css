@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  html {
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: var(--tg-color-bg);
    color: var(--tg-color-text);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  body[data-theme="dark"] {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  }

  body[data-theme="light"] {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  }

  #root {
    min-height: 100vh;
    width: 100%;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-xl font-semibold text-base transition-all duration-200 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-tg-secondary-bg/80 backdrop-blur-sm text-tg-text px-6 py-3 rounded-xl font-semibold text-base transition-all duration-200 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed border border-white/10 hover:bg-tg-secondary-bg;
  }

  .card {
    @apply bg-tg-secondary-bg/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/10 transition-all duration-200 hover:shadow-xl hover:border-white/20;
  }

  .card-premium {
    @apply bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-purple-300/30 transition-all duration-200 hover:shadow-xl hover:border-purple-300/50;
  }

  .glass-effect {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  .gradient-border {
    @apply relative overflow-hidden;
  }

  .gradient-border::before {
    content: '';
    @apply absolute inset-0 p-[2px] bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }

  .nft-card {
    @apply relative overflow-hidden rounded-2xl transition-all duration-300 hover:scale-105 cursor-pointer;
  }

  .nft-card::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 transition-opacity duration-300;
  }

  .nft-card:hover::before {
    @apply opacity-100;
  }

  .rarity-glow {
    @apply relative;
  }

  .rarity-glow::before {
    content: '';
    @apply absolute -inset-1 rounded-2xl blur-sm opacity-75 transition-opacity duration-300;
  }

  .rarity-common::before {
    @apply bg-gray-400;
  }

  .rarity-rare::before {
    @apply bg-blue-400;
  }

  .rarity-epic::before {
    @apply bg-purple-400;
  }

  .rarity-legendary::before {
    @apply bg-yellow-400;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}
