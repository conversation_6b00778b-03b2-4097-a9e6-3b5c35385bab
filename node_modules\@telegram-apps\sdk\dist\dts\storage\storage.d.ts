import { BackButtonState } from '../components/BackButton/types.js';
import { BiometryManagerState } from '../components/BiometryManager/types.js';
import { ClosingBehaviorState } from '../components/ClosingBehavior/types.js';
import { MainButtonState } from '../components/MainButton/types.js';
import { MiniAppState } from '../components/MiniApp/types.js';
import { SettingsButtonState } from '../components/SettingsButton/types.js';
import { ThemeParamsParsed } from '../components/ThemeParams/types.js';
import { ViewportState } from '../components/Viewport/types.js';
import { SwipeBehaviorState } from '../components/SwipeBehavior/types.js';

/**
 * Describes storage keys and according values.
 */
export interface StorageParams {
    backButton: BackButtonState;
    biometryManager: BiometryManagerState;
    closingBehavior: ClosingBehaviorState;
    launchParams: string;
    mainButton: MainButtonState;
    miniApp: MiniAppState;
    settingsButton: SettingsButtonState;
    swipeBehavior: SwipeBehaviorState;
    themeParams: ThemeParamsParsed;
    viewport: ViewportState;
}
/**
 * Key which could be used to store data in the storage.
 */
export type StorageKey = keyof StorageParams;
/**
 * Type specific to the specified storage key.
 */
export type StorageValue<K extends StorageKey> = StorageParams[K];
/**
 * Saves value in the storage.
 * @param key - storage key.
 * @param value - storage value.
 */
export declare function setStorageValue<K extends StorageKey>(key: K, value: StorageValue<K>): void;
/**
 * Extracts value from the storage.
 * @param key - storage key.
 */
export declare function getStorageValue<K extends StorageKey>(key: K): StorageValue<K> | undefined;
