import { StorageKey, StorageValue } from '../../storage/storage.js';
import { FactoryDynamic, FactoryStatic, InitStaticComponentFn, InitDynamicComponentFn, WithOnChange } from './types.js';

/**
 * Creates a new init function based on factory, creating a component, not synchronizing its
 * state with the session storage.
 * @param factory - function creating new component instance.
 */
export declare function createComponentInitFn<R>(factory: FactoryStatic<R>): InitStaticComponentFn<R>;
/**
 * Creates a new init function based on factory, creating a component, synchronizing its
 * state with the session storage.
 * @param factory - function creating new component instance.
 * @param storageKey - storage key to restore component from.
 */
export declare function createComponentInitFn<SK extends StorageKey, R extends WithOnChange<StorageValue<SK>> | Promise<WithOnChange<StorageValue<SK>>>>(storageKey: SK, factory: FactoryDynamic<R, SK>): InitDynamicComponentFn<R>;
