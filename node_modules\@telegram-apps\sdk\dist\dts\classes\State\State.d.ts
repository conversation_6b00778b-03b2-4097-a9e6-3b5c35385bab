import { EventEmitter } from '../../events/event-emitter/EventEmitter.js';
import { StateEvents } from './types.js';
import { StringKeys } from '../../types/utils.js';

type Emitter<State extends object> = EventEmitter<StateEvents<State>>;
export declare class State<State extends object> {
    /**
     * Initial state.
     */
    private readonly state;
    private readonly ee;
    constructor(
    /**
     * Initial state.
     */
    state: State);
    /**
     * Clones current state and returns its copy.
     */
    clone(): State;
    /**
     * Sets value by key.
     * @param key - state key.
     * @param value - value to set.
     */
    set<K extends StringKeys<State>>(key: K, value: State[K]): void;
    /**
     * Sets several values simultaneously.
     * @param state - partial state.
     */
    set(state: Partial<State>): void;
    /**
     * Returns value by specified key.
     * @param key - state key.
     */
    get<K extends StringKeys<State>>(key: K): State[K];
    /**
     * Adds new event listener.
     */
    on: Emitter<State>['on'];
    /**
     * Removes event listener.
     */
    off: Emitter<State>['off'];
}
export {};
