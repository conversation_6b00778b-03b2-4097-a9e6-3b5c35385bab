# Telegram NFT Gifts Mini App

Полнофункциональное Telegram Mini App для коллекционирования NFT подарков на блокчейне TON.

## 🎯 Особенности

- **Сундуки с сокровищами**: Открывайте сундуки разных типов для получения NFT подарков
- **Система лотереи**: Участвуйте в еженедельных розыгрышах эксклюзивных NFT
- **Галерея NFT**: Просматривайте и управляйте своей коллекцией
- **TON Connect**: Интеграция с кошельками TON
- **Анимации Lottie**: Красивые анимированные NFT подарки
- **Apple HIG дизайн**: Современный UI/UX в стиле iOS

## 🚀 Быстрый старт

### 1. Настройка бота

1. Создайте бота через [@BotFather](https://t.me/BotFather)
2. Получите токен бота
3. Обновите `bot/.env` с вашим токеном:

```env
BOT_TOKEN=ваш_токен_бота
WEBAPP_URL=https://ваш-домен.com
PORT=3000
```

### 2. Запуск фронтенда

```bash
# Установка зависимостей
npm install

# Запуск в режиме разработки
npm run dev

# Сборка для продакшена
npm run build
```

### 3. Запуск бота

```bash
cd bot
npm install
npm start
```

## 📱 Структура проекта

```
telegram-miniapp/
├── src/                    # React приложение
│   ├── components/         # React компоненты
│   ├── hooks/             # Пользовательские хуки
│   ├── types/             # TypeScript типы
│   └── main.tsx           # Точка входа
├── bot/                   # Telegram бот
│   ├── index.js           # Основной файл бота
│   └── package.json       # Зависимости бота
├── public/                # Статические файлы
└── dist/                  # Собранное приложение
```

## 🎮 Функциональность

### Сундуки с сокровищами

- **Demo Chest** (Бесплатно): Тренировочный сундук с повышенными шансами
- **Bronze Chest** (50 ⭐): Базовый сундук с обычными наградами
- **Silver Chest** (150 ⭐): Премиум сундук с лучшими шансами
- **Gold Chest** (0.1 TON): Люксовый сундук с редкими NFT

### Система редкости

- **Common** (70%): Обычные подарки
- **Rare** (25%): Редкие подарки
- **Epic** (4%): Эпические подарки
- **Legendary** (1%): Легендарные подарки

### Лотерея

- Покупайте билеты за Stars или TON
- Выполняйте задания для получения бесплатных билетов
- Еженедельные розыгрыши эксклюзивных NFT

## 🔧 Технологии

### Фронтенд
- **React 18** с TypeScript
- **Vite** для быстрой разработки
- **Tailwind CSS** для стилизации
- **Framer Motion** для анимаций
- **TON Connect** для интеграции с кошельками
- **Lottie Web** для анимированных NFT

### Бэкенд
- **Node.js** с Express
- **Telegram Bot API**
- **CORS** для кроссдоменных запросов

## 🌐 Развертывание

### Vercel (Рекомендуется для фронтенда)

1. Подключите репозиторий к Vercel
2. Настройте переменные окружения
3. Деплой произойдет автоматически

### Heroku (Для бота)

1. Создайте приложение Heroku
2. Добавьте переменные окружения
3. Деплойте бота

```bash
git subtree push --prefix=bot heroku main
```

### Railway/Render (Альтернативы)

Аналогично настройте переменные окружения и деплойте.

## 🔐 Безопасность

- Все транзакции проходят через TON Connect
- Валидация данных на стороне сервера
- Защита от CSRF атак
- Безопасное хранение токенов

## 📊 Мониторинг

- Логирование всех операций
- Отслеживание ошибок
- Метрики производительности
- Аналитика пользователей

## 🤝 Вклад в проект

1. Форкните репозиторий
2. Создайте ветку для новой функции
3. Внесите изменения
4. Создайте Pull Request

## 📄 Лицензия

MIT License - см. файл LICENSE

## 🆘 Поддержка

Если у вас есть вопросы или проблемы:

1. Проверьте документацию
2. Создайте Issue в GitHub
3. Свяжитесь с командой поддержки

## 🔗 Полезные ссылки

- [Telegram Mini Apps Documentation](https://core.telegram.org/bots/webapps)
- [TON Connect Documentation](https://docs.ton.org/develop/dapps/ton-connect/overview)
- [Fragment NFT Marketplace](https://fragment.com)
- [GetGems Marketplace](https://getgems.io)

---

**Создано с ❤️ для Telegram и TON экосистемы**
