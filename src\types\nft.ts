export interface NFTGift {
  id: string
  name: string
  description: string
  image: string
  animationUrl?: string
  lottieUrl?: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  collection: string
  attributes: {
    background: string
    icon: string
    luckyNumber: number
    pattern?: string
    effect?: string
  }
  value: number // in TON
  isOwned: boolean
  dateReceived?: Date
  fromChest?: string
  fragmentUrl?: string
}

export interface ChestType {
  id: string
  name: string
  description: string
  price: {
    stars?: number
    ton?: number
  }
  image: string
  rewards: {
    rarity: NFTGift['rarity']
    chance: number // percentage
    items: string[] // NFT IDs
  }[]
  isDemo?: boolean
}

export interface LotteryTicket {
  id: string
  userId: string
  drawId: string
  number: string
  purchaseDate: Date
  isWinner?: boolean
}

export interface LotteryDraw {
  id: string
  name: string
  description: string
  startDate: Date
  endDate: Date
  prizes: NFTGift[]
  ticketPrice: {
    stars?: number
    ton?: number
  }
  maxTickets: number
  soldTickets: number
  isActive: boolean
  winnerTickets?: string[]
}

export interface UserBalance {
  stars: number
  ton: number
  tickets: number
}

export interface UserStats {
  totalChestsOpened: number
  totalNFTsCollected: number
  totalValueEarned: number
  favoriteRarity: NFTGift['rarity']
  lotteryWins: number
}
