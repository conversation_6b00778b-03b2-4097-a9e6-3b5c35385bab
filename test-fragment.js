// Тестовый скрипт для проверки подключения к Fragment API
console.log('🔍 Testing Fragment API connections...')

// Список эндпоинтов для тестирования
const endpoints = [
  'https://fragment.com/api/gifts',
  'https://fragment.com/api/nft/gifts',
  'https://fragment.com/gifts/api',
  'https://api.fragment.com/gifts',
  'https://fragment.com/api/marketplace/gifts',
  'https://fragment.com/gifts',
  'https://fragment.com/marketplace/gifts',
  'https://fragment.com/nft/gifts'
]

async function testEndpoint(url) {
  try {
    console.log(`🔍 Testing: ${url}`)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json, text/html, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Referer': 'https://fragment.com/',
        'Origin': 'https://fragment.com'
      }
    })

    console.log(`📊 ${url} - Status: ${response.status} ${response.statusText}`)
    console.log(`📋 Content-Type: ${response.headers.get('content-type')}`)
    
    if (response.ok) {
      const contentType = response.headers.get('content-type')
      
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json()
        console.log(`✅ ${url} - JSON Response:`, data)
        return { url, success: true, type: 'json', data }
      } else if (contentType && contentType.includes('text/html')) {
        const html = await response.text()
        console.log(`📄 ${url} - HTML Response (${html.length} chars)`)
        
        // Ищем JSON в HTML
        const jsonMatches = html.match(/window\.__INITIAL_STATE__\s*=\s*({.*?});/s) ||
                           html.match(/window\.__DATA__\s*=\s*({.*?});/s) ||
                           html.match(/"gifts":\s*(\[.*?\])/s)
        
        if (jsonMatches) {
          try {
            const jsonData = JSON.parse(jsonMatches[1])
            console.log(`🎯 ${url} - Found JSON in HTML:`, jsonData)
            return { url, success: true, type: 'html-with-json', data: jsonData }
          } catch (e) {
            console.log(`❌ ${url} - Failed to parse JSON from HTML`)
          }
        }
        
        return { url, success: true, type: 'html', data: html.substring(0, 500) + '...' }
      } else {
        const text = await response.text()
        console.log(`📝 ${url} - Text Response:`, text.substring(0, 200) + '...')
        return { url, success: true, type: 'text', data: text.substring(0, 500) }
      }
    } else {
      console.log(`❌ ${url} - Failed: ${response.status} ${response.statusText}`)
      return { url, success: false, status: response.status, statusText: response.statusText }
    }
  } catch (error) {
    console.log(`💥 ${url} - Error: ${error.message}`)
    return { url, success: false, error: error.message }
  }
}

async function testAllEndpoints() {
  console.log('🚀 Starting Fragment API tests...')
  
  const results = []
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint)
    results.push(result)
    
    // Пауза между запросами
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  console.log('\n📊 Test Results Summary:')
  console.log('========================')
  
  const successful = results.filter(r => r.success)
  const failed = results.filter(r => !r.success)
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`)
  console.log(`❌ Failed: ${failed.length}/${results.length}`)
  
  if (successful.length > 0) {
    console.log('\n🎯 Working endpoints:')
    successful.forEach(r => {
      console.log(`  ✅ ${r.url} (${r.type})`)
    })
  }
  
  if (failed.length > 0) {
    console.log('\n💥 Failed endpoints:')
    failed.forEach(r => {
      console.log(`  ❌ ${r.url} - ${r.error || r.statusText}`)
    })
  }
  
  return results
}

// Запускаем тесты
testAllEndpoints().then(results => {
  console.log('\n🏁 Fragment API testing completed!')
  
  // Ищем лучший эндпоинт
  const jsonEndpoints = results.filter(r => r.success && r.type === 'json')
  const htmlWithJsonEndpoints = results.filter(r => r.success && r.type === 'html-with-json')
  
  if (jsonEndpoints.length > 0) {
    console.log(`🏆 Best option: ${jsonEndpoints[0].url} (Direct JSON API)`)
  } else if (htmlWithJsonEndpoints.length > 0) {
    console.log(`🥈 Alternative: ${htmlWithJsonEndpoints[0].url} (HTML with embedded JSON)`)
  } else {
    console.log('😞 No working Fragment API endpoints found')
  }
}).catch(error => {
  console.error('💥 Test failed:', error)
})
