import { MiniAppsMethodName, MiniAppsMethodVersionedParams, MiniAppsMethodWithVersionedParams } from './methods/types/methods.js';
import { Version } from '../version/types.js';

/**
 * Returns true in case, passed parameter in specified method is supported.
 * @param method - method name
 * @param param - method parameter
 * @param inVersion - platform version.
 */
export declare function supports<M extends MiniAppsMethodWithVersionedParams>(method: M, param: MiniAppsMethodVersionedParams<M>, inVersion: Version): boolean;
/**
 * Returns true in case, specified method is supported in passed version.
 * @param method - method name.
 * @param inVersion - platform version.
 */
export declare function supports(method: MiniAppsMethodName, inVersion: Version): boolean;
