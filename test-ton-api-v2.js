// Тестирование разных форматов TON API
console.log('🔍 Testing different TON API formats...')

const TON_API_KEY = '7125266008:c0ad27aa61ea792a52b4e7b39f9b1ed46751'

// Разные базовые URL для тестирования
const API_BASES = [
  'https://tonapi.io/v2',
  'https://tonapi.io/v1', 
  'https://api.ton.org/v2',
  'https://toncenter.com/api/v2',
  'https://mainnet.tonhubapi.com'
]

// Разные форматы авторизации
const AUTH_FORMATS = [
  { type: 'Bearer', value: TON_API_KEY },
  { type: 'X-API-Key', value: TON_API_KEY },
  { type: 'Authorization', value: TON_API_KEY },
  { type: 'api_key', value: TON_API_KEY }
]

const EXAMPLE_NFT_ADDRESS = 'UQA6T1et7YW-j30WUbjp13SWYgFOCPa806-vIXXIMYin2kxx'

async function testDifferentAPIs() {
  console.log('🚀 Testing different API configurations...')
  
  for (const baseUrl of API_BASES) {
    console.log(`\n🔍 Testing base URL: ${baseUrl}`)
    
    for (const auth of AUTH_FORMATS) {
      console.log(`  🔑 Testing auth: ${auth.type}`)
      
      const result = await testAPICall(baseUrl, auth)
      if (result.success) {
        console.log(`  ✅ SUCCESS with ${baseUrl} + ${auth.type}`)
        return { baseUrl, auth, data: result.data }
      } else {
        console.log(`  ❌ Failed: ${result.error}`)
      }
    }
  }
  
  console.log('\n😞 All API configurations failed')
  return null
}

async function testAPICall(baseUrl, auth) {
  try {
    // Тестируем разные эндпоинты
    const endpoints = [
      `/nfts/${EXAMPLE_NFT_ADDRESS}`,
      `/nft/getInfo?address=${EXAMPLE_NFT_ADDRESS}`,
      `/account?address=${EXAMPLE_NFT_ADDRESS}`,
      `/getAddressInformation?address=${EXAMPLE_NFT_ADDRESS}`,
      `/jsonRPC`,
      `/runGetMethod`
    ]
    
    for (const endpoint of endpoints) {
      try {
        const headers = {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
        
        // Добавляем авторизацию в зависимости от типа
        if (auth.type === 'Bearer') {
          headers['Authorization'] = `Bearer ${auth.value}`
        } else if (auth.type === 'X-API-Key') {
          headers['X-API-Key'] = auth.value
        } else {
          headers[auth.type] = auth.value
        }
        
        const url = `${baseUrl}${endpoint}`
        console.log(`    🔍 Trying: ${url}`)
        
        const response = await fetch(url, { headers })
        
        console.log(`    📊 Status: ${response.status} ${response.statusText}`)
        
        if (response.ok) {
          const data = await response.json()
          console.log(`    ✅ Success! Data:`, data)
          return { success: true, data, endpoint, baseUrl, auth }
        }
        
      } catch (e) {
        console.log(`    ❌ Endpoint ${endpoint} error: ${e.message}`)
      }
    }
    
    return { success: false, error: 'All endpoints failed' }
    
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// Тестируем публичные эндпоинты без авторизации
async function testPublicEndpoints() {
  console.log('\n🌐 Testing public endpoints...')
  
  const publicAPIs = [
    'https://toncenter.com/api/v2/getAddressInformation?address=' + EXAMPLE_NFT_ADDRESS,
    'https://toncenter.com/api/v2/runGetMethod',
    'https://mainnet.tonhubapi.com/block/latest',
    'https://tonapi.io/v2/blockchain/accounts/' + EXAMPLE_NFT_ADDRESS,
    'https://api.ton.org/v2/accounts/' + EXAMPLE_NFT_ADDRESS
  ]
  
  for (const url of publicAPIs) {
    try {
      console.log(`🔍 Testing: ${url}`)
      
      const response = await fetch(url, {
        headers: {
          'Accept': 'application/json'
        }
      })
      
      console.log(`📊 Status: ${response.status} ${response.statusText}`)
      
      if (response.ok) {
        const data = await response.json()
        console.log(`✅ Public API success:`, data)
        return { success: true, data, url }
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
    }
  }
  
  return null
}

// Тестируем TON Center API с POST запросами
async function testTonCenterAPI() {
  console.log('\n🏢 Testing TON Center API...')
  
  try {
    // Метод 1: getAddressInformation
    console.log('🔍 Method 1: getAddressInformation')
    const response1 = await fetch('https://toncenter.com/api/v2/getAddressInformation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': TON_API_KEY
      },
      body: JSON.stringify({
        address: EXAMPLE_NFT_ADDRESS
      })
    })
    
    if (response1.ok) {
      const data1 = await response1.json()
      console.log('✅ getAddressInformation success:', data1)
    } else {
      console.log(`❌ getAddressInformation failed: ${response1.status}`)
    }
    
    // Метод 2: runGetMethod для получения NFT данных
    console.log('🔍 Method 2: runGetMethod')
    const response2 = await fetch('https://toncenter.com/api/v2/runGetMethod', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': TON_API_KEY
      },
      body: JSON.stringify({
        address: EXAMPLE_NFT_ADDRESS,
        method: 'get_nft_data',
        stack: []
      })
    })
    
    if (response2.ok) {
      const data2 = await response2.json()
      console.log('✅ runGetMethod success:', data2)
      return data2
    } else {
      console.log(`❌ runGetMethod failed: ${response2.status}`)
    }
    
  } catch (error) {
    console.log(`❌ TON Center API error: ${error.message}`)
  }
  
  return null
}

// Запускаем все тесты
async function runAllTests() {
  console.log('🚀 Starting comprehensive TON API tests...')
  
  // Тест 1: Разные конфигурации API
  const apiResult = await testDifferentAPIs()
  
  // Тест 2: Публичные эндпоинты
  const publicResult = await testPublicEndpoints()
  
  // Тест 3: TON Center API
  const tonCenterResult = await testTonCenterAPI()
  
  console.log('\n📊 Final Results:')
  console.log(`✅ API Configurations: ${apiResult ? 'SUCCESS' : 'FAILED'}`)
  console.log(`✅ Public Endpoints: ${publicResult ? 'SUCCESS' : 'FAILED'}`)
  console.log(`✅ TON Center API: ${tonCenterResult ? 'SUCCESS' : 'FAILED'}`)
  
  if (apiResult) {
    console.log(`🎯 Working API: ${apiResult.baseUrl} with ${apiResult.auth.type}`)
  }
  
  if (publicResult) {
    console.log(`🌐 Working public API: ${publicResult.url}`)
  }
  
  return {
    apiResult,
    publicResult,
    tonCenterResult
  }
}

runAllTests().then(results => {
  console.log('\n🏁 All tests completed!')
  
  if (results.apiResult || results.publicResult || results.tonCenterResult) {
    console.log('🎉 Found working API configuration!')
  } else {
    console.log('😞 No working API configuration found')
    console.log('💡 Suggestions:')
    console.log('  1. Check if API key is valid')
    console.log('  2. Try different API providers')
    console.log('  3. Use public endpoints without auth')
  }
}).catch(error => {
  console.error('💥 Test suite failed:', error)
})
