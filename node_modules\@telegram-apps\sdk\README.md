# @telegram-apps/sdk

[code-badge]: https://img.shields.io/badge/source-black?logo=github

[docs-badge]: https://img.shields.io/badge/documentation-blue?logo=gitbook&logoColor=white

[link]: https://github.com/Telegram-Mini-Apps/telegram-apps/tree/master/packages/sdk

[docs-link]: https://docs.telegram-mini-apps.com/packages/telegram-apps-sdk

[npm-link]: https://npmjs.com/package/@telegram-apps/sdk

[npm-badge]: https://img.shields.io/npm/v/@telegram-apps/sdk?logo=npm

[size-badge]: https://img.shields.io/bundlephobia/minzip/@telegram-apps/sdk

[![NPM][npm-badge]][npm-link]
![Size][size-badge]
[![docs-badge]][docs-link]
[![code-badge]][link]

Made from scratch TypeScript library for seamless communication with Telegram Mini Apps
functionality.

The code of this library is designed to simplify the process of developers interacting with Telegram
Mini Apps. It consists of several individual components, each responsible for a specific aspect of
the Telegram Mini Apps ecosystem.

Before you begin using the SDK, we highly recommend familiarizing yourself with the Telegram Mini
Apps [documentation](https://docs.telegram-mini-apps.com/platform/about)
to grasp the fundamental concepts of the platform.